"""
GPT助手模块
===========

提供GPT模型调用和智能分析功能，现在基于多AI助手实现
"""
import time
import random
import logging
from typing import Optional
from .multi_ai_helper import multi_ai_helper

# 配置日志
logger = logging.getLogger(__name__)

class GPTHelper:
    """GPT助手类，提供重试机制和错误处理
    现在使用多AI助手作为后端，支持多个AI提供商
    """

    def __init__(self, max_retries: int = 3, retry_delay: float = 1.0):
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.model = "gpt-4o-mini"
        self.temperature = 0.7

        # 使用多AI助手
        self.multi_ai = multi_ai_helper

    def _retry_with_backoff(self, func, *args, **kwargs):
        """带指数退避的重试机制"""
        for attempt in range(self.max_retries):
            try:
                result = func(*args, **kwargs)
                if result is not None:
                    return result
            except Exception as e:
                error_type = type(e).__name__
                error_msg = str(e)
                
                logger.warning(f"GPT调用失败 (尝试 {attempt + 1}/{self.max_retries}): {error_type}: {error_msg}")
                
                if attempt < self.max_retries - 1:
                    # 指数退避：每次重试延迟时间翻倍，并加入随机抖动
                    delay = self.retry_delay * (2 ** attempt) + random.uniform(0, 1)
                    logger.info(f"等待 {delay:.2f} 秒后重试...")
                    time.sleep(delay)
                else:
                    logger.error(f"GPT调用最终失败: {error_type}: {error_msg}")
                    raise e
        
        return None

    def get_analysis(self, prompt: str, provider: str = None) -> Optional[str]:
        """获取AI分析
        
        Args:
            prompt: 分析提示词
            provider: AI提供商 ('openai', 'qwen', 'volcengine')
        
        Returns:
            AI分析结果
        """
        try:
            return self._retry_with_backoff(
                self.multi_ai.call_ai,
                prompt,
                provider or 'openai'
            )
        except Exception as e:
            logger.error(f"获取AI分析失败: {e}")
            return None

    def analyze_etf_data(self, etf_data: dict, market_context: str = "") -> Optional[str]:
        """分析ETF数据
        
        Args:
            etf_data: ETF数据字典
            market_context: 市场背景信息
        
        Returns:
            分析结果
        """
        try:
            # 构建分析提示词
            prompt = self._build_etf_analysis_prompt(etf_data, market_context)
            return self.get_analysis(prompt)
        except Exception as e:
            logger.error(f"ETF数据分析失败: {e}")
            return None

    def _build_etf_analysis_prompt(self, etf_data: dict, market_context: str = "") -> str:
        """构建ETF分析提示词"""
        prompt = f"""
请分析以下ETF的投资情况：

ETF基本信息：
- 名称：{etf_data.get('name', '未知')}
- 代码：{etf_data.get('symbol', '未知')}
- 板块：{etf_data.get('sector', '未知')}
- 风险等级：{etf_data.get('risk_level', '中')}

价格信息：
- 当前价格：{etf_data.get('current_price', 0):.3f}元
- 成本价格：{etf_data.get('cost_price', 0):.3f}元
- 历史高点：{etf_data.get('high_price', 0):.3f}元
- 持仓数量：{etf_data.get('position', 0)}股

投资策略：{etf_data.get('strategy', 'value')}

市场背景：{market_context}

请从以下角度进行分析：
1. 当前估值水平分析
2. 技术面分析（相对高点和成本价的位置）
3. 基本面分析（板块前景和风险）
4. 具体操作建议（买入/卖出/持有及理由）
5. 风险提示

请提供专业、客观的投资建议。
"""
        return prompt

    def get_portfolio_analysis(self, portfolio_data: list, market_summary: str = "") -> Optional[str]:
        """获取投资组合分析
        
        Args:
            portfolio_data: 投资组合数据列表
            market_summary: 市场总结
        
        Returns:
            投资组合分析结果
        """
        try:
            prompt = self._build_portfolio_analysis_prompt(portfolio_data, market_summary)
            return self.get_analysis(prompt)
        except Exception as e:
            logger.error(f"投资组合分析失败: {e}")
            return None

    def _build_portfolio_analysis_prompt(self, portfolio_data: list, market_summary: str = "") -> str:
        """构建投资组合分析提示词"""
        # 计算组合统计信息
        total_value = sum(etf.get('current_price', 0) * etf.get('position', 0) for etf in portfolio_data)
        total_cost = sum(etf.get('cost_price', 0) * etf.get('position', 0) for etf in portfolio_data)
        total_return = ((total_value - total_cost) / total_cost * 100) if total_cost > 0 else 0

        # 板块分布
        sector_dist = {}
        for etf in portfolio_data:
            sector = etf.get('sector', '未知')
            value = etf.get('current_price', 0) * etf.get('position', 0)
            sector_dist[sector] = sector_dist.get(sector, 0) + value

        prompt = f"""
请分析以下ETF投资组合：

组合概况：
- 总市值：{total_value:.2f}元
- 总成本：{total_cost:.2f}元
- 总收益率：{total_return:.2f}%
- ETF数量：{len(portfolio_data)}只

板块分布：
"""
        for sector, value in sector_dist.items():
            weight = (value / total_value * 100) if total_value > 0 else 0
            prompt += f"- {sector}：{value:.2f}元 ({weight:.1f}%)\n"

        prompt += f"""
个股详情：
"""
        for etf in portfolio_data:
            current_price = etf.get('current_price', 0)
            cost_price = etf.get('cost_price', 0)
            position = etf.get('position', 0)
            return_rate = ((current_price - cost_price) / cost_price * 100) if cost_price > 0 else 0
            
            prompt += f"- {etf.get('name', '未知')}({etf.get('symbol', '')})：{current_price:.3f}元，收益率{return_rate:.2f}%\n"

        prompt += f"""
市场环境：{market_summary}

请从以下角度分析：
1. 组合整体表现评估
2. 板块配置合理性分析
3. 风险分散程度评估
4. 个股表现分析
5. 组合优化建议
6. 市场风险提示

请提供专业的投资组合管理建议。
"""
        return prompt

    def get_market_analysis(self, market_data: dict) -> Optional[str]:
        """获取市场分析
        
        Args:
            market_data: 市场数据
        
        Returns:
            市场分析结果
        """
        try:
            prompt = f"""
请分析当前市场环境：

市场数据：{market_data}

请从以下角度分析：
1. 宏观经济环境
2. 市场情绪和资金流向
3. 主要指数表现
4. 热点板块分析
5. 风险因素识别
6. 投资策略建议

请提供专业的市场分析。
"""
            return self.get_analysis(prompt)
        except Exception as e:
            logger.error(f"市场分析失败: {e}")
            return None

# 全局实例
gpt_helper = GPTHelper()

def get_gpt_analysis(prompt: str, provider: str = None) -> Optional[str]:
    """获取GPT分析（兼容性函数）"""
    return gpt_helper.get_analysis(prompt, provider)
