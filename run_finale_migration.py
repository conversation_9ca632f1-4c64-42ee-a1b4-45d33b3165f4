#!/usr/bin/env python3
"""
Launcher script for Finale Supplier Data Migration
This script can be run directly from PyCharm or command line
"""

import sys
import os

# Add the current directory to Python path to ensure imports work
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

# Import and run the migration script
from finale.MigrateFinaleSupplierData import main

if __name__ == "__main__":
    main()
