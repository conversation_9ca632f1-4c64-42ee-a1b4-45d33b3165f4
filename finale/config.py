"""
Finale Supplier Data Migration Configuration
配置文件，包含数据库连接、API配置和数据映射关系
"""

import os
from typing import Dict, Any
from urllib.parse import quote_plus


class Config:
    """配置管理类"""
    
    def __init__(self):
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        # 数据库配置
        self.DB_CONFIG = {
            'username': os.getenv('DB_USERNAME', 'item_management_user'),
            'password': os.getenv('DB_PASSWORD', 'mercaso'),
            'host': os.getenv('DB_HOST', 'localhost'),
            'port': os.getenv('DB_PORT', '5432'),
            'database': os.getenv('DB_NAME', 'item_management_service')
        }
        
        # Finale API配置
        self.FINALE_API_CONFIG = {
            'base_url': 'https://app.finaleinventory.com/mercaso/api',
            'timeout': 30,
            'headers': {
                'Content-Type': 'application/json',
                'User-Agent': 'Mercaso-Supplier-Migration/1.0',
                'Authorization': 'Basic Mk5aWWs3MU1mcERvOlpDdWFJREFvNmNIWWV5RERiQ05uZ000cE9CVDdJaWNj'
            }
        }
        
        # 日志配置
        self.LOG_CONFIG = {
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'file': 'finale_migration.log'
        }
    
    def get_db_connection_string(self) -> str:
        """获取数据库连接字符串"""
        username = quote_plus(self.DB_CONFIG['username'])
        password = quote_plus(self.DB_CONFIG['password'])
        host = self.DB_CONFIG['host']
        port = self.DB_CONFIG['port']
        database = self.DB_CONFIG['database']
        
        return f"postgresql+psycopg2://{username}:{password}@{host}:{port}/{database}"


class DataMapping:
    """数据映射配置类"""
    
    # settlementTermId映射
    SETTLEMENT_TERMS_MAPPING = {
        "##null": "Unspecified",
        "NET_15_3_15": "Terms",
        "COD": "Check",
        "NET_10_2_M": "Cash / Check",
        "CREDIT_CARD": "Credit card",
        "ACH": "ACH",
        "NET_15": "Net 14 days",
        "NET_10": "Net 10 days",
        "PREPAID": "Prepaid",
        "##user_00025": "Consignment",
        "PAYPAL": "Bill.com"
    }
    
    # userFieldDataList映射
    USER_FIELD_MAPPING = {
        'user_10009': 'destination_dc',      # 目标配送中心
        'user_10000': 'purchasing_group',    # 采购组
        'user_10001': 'account_num',         # 账户号码
        'user_10002': 'order_method',        # 订单方式
        'user_10003': 'freight',             # 运费方式
        'user_10004': 'delivery_mon',        # 周一配送
        'user_10005': 'delivery_tue',        # 周二配送
        'user_10006': 'delivery_wed',        # 周三配送
        'user_10007': 'delivery_thu',        # 周四配送
        'user_10008': 'delivery_fri'         # 周五配送
    }
    
    # contactMechTypeId映射
    CONTACT_MECH_TYPE_MAPPING = {
        'POSTAL_ADDRESS': 'address',
        'EMAIL_ADDRESS': 'email',
        'TELECOM_NUMBER': 'phone_number',
        'WEB_ADDRESS': 'web_address'
    }

    # contactMechPurposeTypeId映射
    CONTACT_MECH_PURPOSE_MAPPING = {
        "##null": None,  # 空值处理
        "GENERAL_LOCATION": "GENERAL",
        "SHIPPING_LOCATION": "SHIPPING",
        "PAYMENT_LOCATION": "PAYMENT",
        "BILLING_LOCATION": "BILLING"
    }

    # 邮箱类型映射
    EMAIL_TYPE_MAPPING = {
        "##null": None,  # 默认为商务邮箱
        "HOME_EMAIL": "HOME",
        "WORK_EMAIL": "WORK",
        "PAYMENT_EMAIL": "PAYMENT",
        "BILLING_EMAIL": "BILLING"
    }

    # 电话类型映射
    PHONE_TYPE_MAPPING = {
        "##null": None,  # 默认为工作电话
        "PHONE_HOME": "HOME",
        "PHONE_WORK": "WORK",
        "PHONE_MOBILE": "MOBILE",
        "FAX_NUMBER": "FAX",
        "PHONE_DID": "OTHER"  # Direct映射为OTHER
    }
    
    # 布尔值字段列表（用于userFieldDataList中的配送日期）
    BOOLEAN_FIELDS = {
        'delivery_mon', 'delivery_tue', 'delivery_wed', 
        'delivery_thu', 'delivery_fri'
    }
    
    @classmethod
    def get_settlement_term(cls, term_id: str) -> str:
        """获取结算条款映射"""
        return cls.SETTLEMENT_TERMS_MAPPING.get(term_id, term_id)
    
    @classmethod
    def get_user_field_name(cls, field_id: str) -> str:
        """获取用户字段名映射"""
        return cls.USER_FIELD_MAPPING.get(field_id, field_id)
    
    @classmethod
    def get_contact_mech_table(cls, mech_type: str) -> str:
        """获取联系方式对应的表名"""
        return cls.CONTACT_MECH_TYPE_MAPPING.get(mech_type)
    
    @classmethod
    def is_boolean_field(cls, field_name: str) -> bool:
        """判断是否为布尔值字段"""
        return field_name in cls.BOOLEAN_FIELDS

    @classmethod
    def get_contact_mech_purpose(cls, purpose_id: str) -> str:
        """获取联系方式用途映射"""
        return cls.CONTACT_MECH_PURPOSE_MAPPING.get(purpose_id, purpose_id)

    @classmethod
    def get_email_type(cls, email_type_id: str) -> str:
        """获取邮箱类型映射"""
        return cls.EMAIL_TYPE_MAPPING.get(email_type_id, email_type_id)

    @classmethod
    def get_phone_type(cls, phone_type_id: str) -> str:
        """获取电话类型映射"""
        return cls.PHONE_TYPE_MAPPING.get(phone_type_id, phone_type_id)


class TableConfig:
    """数据库表配置"""
    
    # 表名常量
    VENDOR_TABLE = 'vendor'
    ADDRESS_TABLE = 'address'
    EMAIL_TABLE = 'email'
    PHONE_NUMBER_TABLE = 'phone_number'
    WEB_ADDRESS_TABLE = 'web_address'
    SUPPLIER_ADDITIONAL_INFO_TABLE = 'supplier_additional_info'
    
    # 实体类型常量
    ENTITY_TYPE_VENDOR = 'VENDOR'
    
    # 默认值
    DEFAULT_CREATED_BY = '1'
    DEFAULT_CREATED_USER_NAME = 'Finale Migration System'


# 全局配置实例
config = Config()
data_mapping = DataMapping()
table_config = TableConfig()
