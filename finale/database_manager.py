"""
数据库管理器
用于处理PostgreSQL数据库连接和CRUD操作
"""

import logging
import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
import sqlalchemy
from sqlalchemy import create_engine, text
from sqlalchemy.exc import SQLAlchemyError
from contextlib import contextmanager

# Handle both relative and absolute imports
try:
    from .config import config, table_config
except ImportError:
    from config import config, table_config

logger = logging.getLogger(__name__)


class DatabaseManager:
    """数据库管理器类"""
    
    def __init__(self):
        self.connection_string = config.get_db_connection_string()
        self.engine = None
        self._connect()
    
    def _connect(self):
        """建立数据库连接"""
        try:
            self.engine = create_engine(
                self.connection_string,
                pool_pre_ping=True,
                pool_recycle=3600
            )
            # 测试连接
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.info("数据库连接成功")
        except Exception as e:
            logger.error(f"数据库连接失败: {e}")
            raise
    
    @contextmanager
    def get_connection(self):
        """获取数据库连接上下文管理器"""
        connection = self.engine.connect()
        transaction = connection.begin()
        try:
            yield connection
            transaction.commit()
        except Exception:
            transaction.rollback()
            raise
        finally:
            connection.close()
    
    def find_vendor_by_finale_id(self, finale_id: str) -> Optional[Dict[str, Any]]:
        """
        根据finale_id查找vendor
        
        Args:
            finale_id: Finale系统中的partyId
        
        Returns:
            Dict: vendor信息，如果不存在返回None
        """
        query = text("""
            SELECT id, vendor_name, finale_id, created_at, updated_at
            FROM vendor 
            WHERE finale_id = :finale_id AND deleted_at IS NULL
        """)
        
        try:
            with self.get_connection() as conn:
                result = conn.execute(query, {'finale_id': finale_id})
                row = result.fetchone()
                if row:
                    return dict(row._mapping)
                return None
        except SQLAlchemyError as e:
            logger.error(f"查询vendor失败 (finale_id: {finale_id}): {e}")
            return None
    
    def update_vendor(self, vendor_id: str, update_data: Dict[str, Any]) -> bool:
        """
        更新vendor信息
        
        Args:
            vendor_id: vendor的UUID
            update_data: 要更新的数据
        
        Returns:
            bool: 更新是否成功
        """
        if not update_data:
            return True
        
        # 构建更新语句
        set_clauses = []
        params = {'vendor_id': vendor_id}
        
        for key, value in update_data.items():
            if key in ['default_terms', 'default_lead_days', 'notes', 'vendor_contact_name']:
                set_clauses.append(f"{key} = :{key}")
                params[key] = value
        
        if not set_clauses:
            return True
        
        # 添加更新时间和更新人
        set_clauses.extend([
            "updated_at = :updated_at",
            "updated_by = :updated_by",
            "updated_user_name = :updated_user_name"
        ])
        params.update({
            'updated_at': datetime.now(),
            'updated_by': table_config.DEFAULT_CREATED_BY,
            'updated_user_name': table_config.DEFAULT_CREATED_USER_NAME
        })
        
        query = text(f"""
            UPDATE vendor 
            SET {', '.join(set_clauses)}
            WHERE id = :vendor_id AND deleted_at IS NULL
        """)
        
        try:
            with self.get_connection() as conn:
                result = conn.execute(query, params)
                success = result.rowcount > 0
                if success:
                    logger.info(f"成功更新vendor: {vendor_id}")
                else:
                    logger.warning(f"vendor更新失败，可能不存在: {vendor_id}")
                return success
        except SQLAlchemyError as e:
            logger.error(f"更新vendor失败 (id: {vendor_id}): {e}")
            return False
    
    def delete_vendor_contacts(self, vendor_id: str) -> bool:
        """
        删除vendor的所有联系方式信息（软删除）
        
        Args:
            vendor_id: vendor的UUID
        
        Returns:
            bool: 删除是否成功
        """
        tables = [
            table_config.ADDRESS_TABLE,
            table_config.EMAIL_TABLE,
            table_config.PHONE_NUMBER_TABLE,
            table_config.WEB_ADDRESS_TABLE
        ]
        
        try:
            with self.get_connection() as conn:
                for table in tables:
                    query = text(f"""
                        UPDATE {table}
                        SET deleted_at = :deleted_at,
                            deleted_by = :deleted_by,
                            deleted_user_name = :deleted_user_name
                        WHERE entity_type = :entity_type 
                        AND entity_id = :entity_id 
                        AND deleted_at IS NULL
                    """)
                    
                    conn.execute(query, {
                        'deleted_at': datetime.now(),
                        'deleted_by': table_config.DEFAULT_CREATED_BY,
                        'deleted_user_name': table_config.DEFAULT_CREATED_USER_NAME,
                        'entity_type': table_config.ENTITY_TYPE_VENDOR,
                        'entity_id': vendor_id
                    })
                
                logger.info(f"成功删除vendor联系方式: {vendor_id}")
                return True
        except SQLAlchemyError as e:
            logger.error(f"删除vendor联系方式失败 (id: {vendor_id}): {e}")
            return False
    
    def insert_address(self, vendor_id: str, address_data: Dict[str, Any]) -> bool:
        """插入地址信息"""
        query = text("""
            INSERT INTO address (
                id, entity_type, entity_id, street_address, city, state, 
                postal_code, country, directions, purpose, additional_lines,
                created_at, created_by, created_user_name,
                updated_at, updated_by, updated_user_name
            ) VALUES (
                :id, :entity_type, :entity_id, :street_address, :city, :state,
                :postal_code, :country, :directions, :purpose, :additional_lines,
                :created_at, :created_by, :created_user_name,
                :updated_at, :updated_by, :updated_user_name
            )
        """)
        
        params = {
            'id': str(uuid.uuid4()),
            'entity_type': table_config.ENTITY_TYPE_VENDOR,
            'entity_id': vendor_id,
            'street_address': address_data.get('street_address', ''),
            'city': address_data.get('city', ''),
            'state': address_data.get('state'),
            'postal_code': address_data.get('postal_code'),
            'country': address_data.get('country'),
            'directions': address_data.get('directions'),
            'purpose': address_data.get('purpose', 'UNKNOWN'),
            'additional_lines': address_data.get('additional_lines'),
            'created_at': datetime.now(),
            'created_by': table_config.DEFAULT_CREATED_BY,
            'created_user_name': table_config.DEFAULT_CREATED_USER_NAME,
            'updated_at': datetime.now(),
            'updated_by': table_config.DEFAULT_CREATED_BY,
            'updated_user_name': table_config.DEFAULT_CREATED_USER_NAME
        }
        
        try:
            with self.get_connection() as conn:
                conn.execute(query, params)
                return True
        except SQLAlchemyError as e:
            logger.error(f"插入地址失败: {e}")
            return False

    def insert_email(self, vendor_id: str, email_data: Dict[str, Any]) -> bool:
        """插入邮箱信息"""
        query = text("""
            INSERT INTO email (
                id, entity_type, entity_id, email_type, email, extension,
                created_at, created_by, created_user_name,
                updated_at, updated_by, updated_user_name
            ) VALUES (
                :id, :entity_type, :entity_id, :email_type, :email, :extension,
                :created_at, :created_by, :created_user_name,
                :updated_at, :updated_by, :updated_user_name
            )
        """)

        params = {
            'id': str(uuid.uuid4()),
            'entity_type': table_config.ENTITY_TYPE_VENDOR,
            'entity_id': vendor_id,
            'email_type': email_data.get('email_type', 'UNKNOWN'),
            'email': email_data.get('email', ''),
            'extension': email_data.get('extension'),
            'created_at': datetime.now(),
            'created_by': table_config.DEFAULT_CREATED_BY,
            'created_user_name': table_config.DEFAULT_CREATED_USER_NAME,
            'updated_at': datetime.now(),
            'updated_by': table_config.DEFAULT_CREATED_BY,
            'updated_user_name': table_config.DEFAULT_CREATED_USER_NAME
        }

        try:
            with self.get_connection() as conn:
                conn.execute(query, params)
                return True
        except SQLAlchemyError as e:
            logger.error(f"插入邮箱失败: {e}")
            return False

    def insert_phone_number(self, vendor_id: str, phone_data: Dict[str, Any]) -> bool:
        """插入电话信息"""
        query = text("""
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name,
                updated_at, updated_by, updated_user_name
            ) VALUES (
                :id, :entity_type, :entity_id, :phone_type, :phone_number, :extension,
                :created_at, :created_by, :created_user_name,
                :updated_at, :updated_by, :updated_user_name
            )
        """)

        params = {
            'id': str(uuid.uuid4()),
            'entity_type': table_config.ENTITY_TYPE_VENDOR,
            'entity_id': vendor_id,
            'phone_type': phone_data.get('phone_type', 'UNKNOWN'),
            'phone_number': phone_data.get('phone_number', ''),
            'extension': phone_data.get('extension'),
            'created_at': datetime.now(),
            'created_by': table_config.DEFAULT_CREATED_BY,
            'created_user_name': table_config.DEFAULT_CREATED_USER_NAME,
            'updated_at': datetime.now(),
            'updated_by': table_config.DEFAULT_CREATED_BY,
            'updated_user_name': table_config.DEFAULT_CREATED_USER_NAME
        }

        try:
            with self.get_connection() as conn:
                conn.execute(query, params)
                return True
        except SQLAlchemyError as e:
            logger.error(f"插入电话失败: {e}")
            return False

    def insert_web_address(self, vendor_id: str, web_data: Dict[str, Any]) -> bool:
        """插入网址信息"""
        query = text("""
            INSERT INTO web_address (
                id, entity_type, entity_id, web_address_type, web_address,
                created_at, created_by, created_user_name,
                updated_at, updated_by, updated_user_name
            ) VALUES (
                :id, :entity_type, :entity_id, :web_address_type, :web_address,
                :created_at, :created_by, :created_user_name,
                :updated_at, :updated_by, :updated_user_name
            )
        """)

        params = {
            'id': str(uuid.uuid4()),
            'entity_type': table_config.ENTITY_TYPE_VENDOR,
            'entity_id': vendor_id,
            'web_address_type': web_data.get('web_address_type', 'WEBSITE'),
            'web_address': web_data.get('web_address', ''),
            'created_at': datetime.now(),
            'created_by': table_config.DEFAULT_CREATED_BY,
            'created_user_name': table_config.DEFAULT_CREATED_USER_NAME,
            'updated_at': datetime.now(),
            'updated_by': table_config.DEFAULT_CREATED_BY,
            'updated_user_name': table_config.DEFAULT_CREATED_USER_NAME
        }

        try:
            with self.get_connection() as conn:
                conn.execute(query, params)
                return True
        except SQLAlchemyError as e:
            logger.error(f"插入网址失败: {e}")
            return False

    def insert_supplier_additional_info(self, vendor_id: str, additional_data: Dict[str, Any]) -> bool:
        """插入supplier额外信息"""
        query = text("""
            INSERT INTO supplier_additional_info (
                id, vendor_id, destination_dc, purchasing_group, account_num,
                order_method, freight, delivery_mon, delivery_tue, delivery_wed,
                delivery_thu, delivery_fri, created_at, created_by, created_user_name,
                updated_at, updated_by, updated_user_name
            ) VALUES (
                :id, :vendor_id, :destination_dc, :purchasing_group, :account_num,
                :order_method, :freight, :delivery_mon, :delivery_tue, :delivery_wed,
                :delivery_thu, :delivery_fri, :created_at, :created_by, :created_user_name,
                :updated_at, :updated_by, :updated_user_name
            )
        """)

        params = {
            'id': str(uuid.uuid4()),
            'vendor_id': vendor_id,
            'destination_dc': additional_data.get('destination_dc'),
            'purchasing_group': additional_data.get('purchasing_group'),
            'account_num': additional_data.get('account_num'),
            'order_method': additional_data.get('order_method'),
            'freight': additional_data.get('freight'),
            'delivery_mon': additional_data.get('delivery_mon', False),
            'delivery_tue': additional_data.get('delivery_tue', False),
            'delivery_wed': additional_data.get('delivery_wed', False),
            'delivery_thu': additional_data.get('delivery_thu', False),
            'delivery_fri': additional_data.get('delivery_fri', False),
            'created_at': datetime.now(),
            'created_by': table_config.DEFAULT_CREATED_BY,
            'created_user_name': table_config.DEFAULT_CREATED_USER_NAME,
            'updated_at': datetime.now(),
            'updated_by': table_config.DEFAULT_CREATED_BY,
            'updated_user_name': table_config.DEFAULT_CREATED_USER_NAME
        }

        try:
            with self.get_connection() as conn:
                conn.execute(query, params)
                return True
        except SQLAlchemyError as e:
            logger.error(f"插入supplier额外信息失败: {e}")
            return False

    def delete_supplier_additional_info(self, vendor_id: str) -> bool:
        """删除supplier额外信息（软删除）"""
        query = text("""
            UPDATE supplier_additional_info
            SET deleted_at = :deleted_at,
                deleted_by = :deleted_by,
                deleted_user_name = :deleted_user_name
            WHERE vendor_id = :vendor_id AND deleted_at IS NULL
        """)

        try:
            with self.get_connection() as conn:
                conn.execute(query, {
                    'deleted_at': datetime.now(),
                    'deleted_by': table_config.DEFAULT_CREATED_BY,
                    'deleted_user_name': table_config.DEFAULT_CREATED_USER_NAME,
                    'vendor_id': vendor_id
                })
                return True
        except SQLAlchemyError as e:
            logger.error(f"删除supplier额外信息失败 (vendor_id: {vendor_id}): {e}")
            return False

    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            with self.get_connection() as conn:
                conn.execute(text("SELECT 1"))
                return True
        except Exception as e:
            logger.error(f"数据库连接测试失败: {e}")
            return False


# 便捷函数
def create_database_manager() -> DatabaseManager:
    """创建数据库管理器实例"""
    return DatabaseManager()


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)

    db_manager = create_database_manager()

    if db_manager.test_connection():
        print("数据库连接测试成功")
    else:
        print("数据库连接测试失败")
