# Finale Supplier Data Migration - Running Instructions

The ImportError issue has been fixed. You now have multiple ways to run the migration script:

## Method 1: Direct execution from finale directory
```bash
cd finale
python MigrateFinaleSupplierData.py --dry-run --verbose
```

## Method 2: Run as a Python module (recommended)
From the project root directory (`/Users/<USER>/mercaso_project/split-csv`):
```bash
python -m finale.MigrateFinaleSupplierData --dry-run --verbose
```

## Method 3: Use the launcher script
From the project root directory:
```bash
python run_finale_migration.py --dry-run --verbose
```

## Method 4: Use the local launcher (from finale directory)
```bash
cd finale
python run_migration.py --dry-run --verbose
```

## PyCharm Configuration

### Option A: Configure PyCharm to run as module
1. In PyCharm, go to Run → Edit Configurations
2. Create a new Python configuration
3. Set the following:
   - **Script path**: Leave empty
   - **Module name**: `finale.MigrateFinaleSupplierData`
   - **Parameters**: `--dry-run --verbose`
   - **Working directory**: `/Users/<USER>/mercaso_project/split-csv`

### Option B: Use the launcher script
1. In PyCharm, go to Run → Edit Configurations
2. Create a new Python configuration
3. Set the following:
   - **Script path**: `/Users/<USER>/mercaso_project/split-csv/run_finale_migration.py`
   - **Parameters**: `--dry-run --verbose`
   - **Working directory**: `/Users/<USER>/mercaso_project/split-csv`

## Parameters
- `--dry-run`: Only show what operations would be performed, don't actually modify the database
- `--verbose`: Show detailed logging information

## What was fixed
- Modified all Python files in the finale package to handle both relative and absolute imports
- This allows the scripts to work whether run as standalone files or as part of a package
- Created launcher scripts for easier execution from PyCharm

The import error should no longer occur with any of these methods.
