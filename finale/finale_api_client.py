"""
Finale API客户端
用于调用Finale API获取supplier数据
"""

import requests
import logging
from typing import Dict, List, Optional, Any

# Handle both relative and absolute imports
try:
    from .config import config
except ImportError:
    from config import config

logger = logging.getLogger(__name__)


class FinaleAPIClient:
    """Finale API客户端类"""
    
    def __init__(self):
        self.base_url = config.FINALE_API_CONFIG['base_url']
        self.timeout = config.FINALE_API_CONFIG['timeout']
        self.headers = config.FINALE_API_CONFIG['headers'].copy()
        self.session = requests.Session()
        self.session.headers.update(self.headers)
    
    def get_party_group_list(self) -> Optional[Dict[str, Any]]:
        """
        获取partygroup列表
        
        Returns:
            Dict: 包含partyId, partyUrl等信息的字典，如果失败返回None
        """
        url = f"{self.base_url}/partygroup"
        
        try:
            logger.info(f"正在获取partygroup列表: {url}")
            response = self.session.get(url, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            logger.info(f"成功获取partygroup列表，共 {len(data.get('partyId', []))} 个supplier")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"获取partygroup列表失败: {e}")
            return None
        except ValueError as e:
            logger.error(f"解析partygroup响应JSON失败: {e}")
            return None
    
    def get_party_detail(self, party_url: str) -> Optional[Dict[str, Any]]:
        """
        获取单个party的详细信息
        
        Args:
            party_url: party的URL路径，如 "/mercaso/api/partygroup/100001"
        
        Returns:
            Dict: party详细信息，如果失败返回None
        """
        # 构建完整URL
        if party_url.startswith('/'):
            full_url = f"https://app.finaleinventory.com{party_url}"
        else:
            full_url = f"{self.base_url}/{party_url}"
        
        try:
            logger.debug(f"正在获取party详细信息: {full_url}")
            response = self.session.get(full_url, timeout=self.timeout)
            response.raise_for_status()
            
            data = response.json()
            logger.debug(f"成功获取party详细信息: {party_url}")
            return data
            
        except requests.exceptions.RequestException as e:
            logger.error(f"获取party详细信息失败 {party_url}: {e}")
            return None
        except ValueError as e:
            logger.error(f"解析party详细信息JSON失败 {party_url}: {e}")
            return None
    
    def get_all_suppliers_data(self) -> List[Dict[str, Any]]:
        """
        获取所有supplier的完整数据
        
        Returns:
            List[Dict]: 包含所有supplier详细信息的列表
        """
        suppliers_data = []
        
        # 获取partygroup列表
        party_group_data = self.get_party_group_list()
        if not party_group_data:
            logger.error("无法获取partygroup列表")
            return suppliers_data
        
        party_ids = party_group_data.get('partyId', [])
        party_urls = party_group_data.get('partyUrl', [])
        
        if len(party_ids) != len(party_urls):
            logger.error("partyId和partyUrl数量不匹配")
            return suppliers_data
        
        # 获取每个supplier的详细信息
        for i, (party_id, party_url) in enumerate(zip(party_ids, party_urls)):
            logger.info(f"正在处理supplier {i+1}/{len(party_ids)}: {party_id}")
            
            detail_data = self.get_party_detail(party_url)
            if detail_data:
                # 将基本信息和详细信息合并
                supplier_data = {
                    'partyId': party_id,
                    'partyUrl': party_url,
                    'statusId': party_group_data.get('statusId', [None])[i] if i < len(party_group_data.get('statusId', [])) else None,
                    'lastUpdatedDate': party_group_data.get('lastUpdatedDate', [None])[i] if i < len(party_group_data.get('lastUpdatedDate', [])) else None,
                    'createdDate': party_group_data.get('createdDate', [None])[i] if i < len(party_group_data.get('createdDate', [])) else None,
                    'detail': detail_data
                }
                suppliers_data.append(supplier_data)
            else:
                logger.warning(f"跳过supplier {party_id}，无法获取详细信息")
        
        logger.info(f"成功获取 {len(suppliers_data)} 个supplier的完整数据")
        return suppliers_data
    
    def test_connection(self) -> bool:
        """
        测试API连接
        
        Returns:
            bool: 连接是否成功
        """
        try:
            response = self.session.get(f"{self.base_url}/partygroup", timeout=10)
            return response.status_code == 200
        except Exception as e:
            logger.error(f"API连接测试失败: {e}")
            return False


# 便捷函数
def create_api_client() -> FinaleAPIClient:
    """创建API客户端实例"""
    return FinaleAPIClient()


if __name__ == "__main__":
    # 测试代码
    logging.basicConfig(level=logging.INFO)
    
    client = create_api_client()
    
    # 测试连接
    if client.test_connection():
        print("API连接测试成功")
        
        # 获取partygroup列表
        data = client.get_party_group_list()
        if data:
            print(f"获取到 {len(data.get('partyId', []))} 个supplier")
            
            # 测试获取第一个supplier的详细信息
            if data.get('partyUrl'):
                detail = client.get_party_detail(data['partyUrl'][0])
                if detail:
                    print("成功获取supplier详细信息")
                else:
                    print("获取supplier详细信息失败")
    else:
        print("API连接测试失败")
