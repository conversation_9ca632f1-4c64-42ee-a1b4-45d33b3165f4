#!/usr/bin/env python3
"""
Direct runner for Finale Supplier Data Migration
This script can be run directly from the finale directory
"""

import sys
import os

# Add parent directory to path for absolute imports
parent_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.insert(0, parent_dir)

# Now import and run
if __name__ == "__main__":
    from finale.MigrateFinaleSupplierData import main
    main()
