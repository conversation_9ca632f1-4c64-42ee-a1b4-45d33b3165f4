2025-09-19 15:14:47,404 - __main__ - INFO - 日志系统初始化完成
2025-09-19 15:14:47,404 - __main__ - INFO - 开始Finale Supplier数据迁移
2025-09-19 15:14:47,404 - __main__ - INFO - 验证连接...
2025-09-19 15:14:49,168 - __main__ - INFO - 所有连接验证成功
2025-09-19 15:14:49,168 - __main__ - INFO - 开始获取Finale supplier数据...
2025-09-19 15:14:49,169 - finale_api_client - INFO - 正在获取partygroup列表: https://app.finaleinventory.com/mercaso/api/partygroup
2025-09-19 15:14:49,624 - finale_api_client - INFO - 成功获取partygroup列表，共 215 个supplier
2025-09-19 15:14:49,624 - finale_api_client - INFO - 正在处理supplier 1/215: 100000
2025-09-19 15:14:49,995 - finale_api_client - INFO - 正在处理supplier 2/215: 100001
2025-09-19 15:14:50,343 - finale_api_client - INFO - 正在处理supplier 3/215: 100002
2025-09-19 15:14:50,686 - finale_api_client - INFO - 正在处理supplier 4/215: 100003
2025-09-19 15:14:51,024 - finale_api_client - INFO - 正在处理supplier 5/215: 100004
2025-09-19 15:14:51,390 - finale_api_client - INFO - 正在处理supplier 6/215: 100005
2025-09-19 15:14:51,734 - finale_api_client - INFO - 正在处理supplier 7/215: 100006
2025-09-19 15:14:52,103 - finale_api_client - INFO - 正在处理supplier 8/215: 100007
2025-09-19 15:14:52,451 - finale_api_client - INFO - 正在处理supplier 9/215: 100008
2025-09-19 15:14:52,868 - finale_api_client - INFO - 正在处理supplier 10/215: 100009
2025-09-19 15:14:53,230 - finale_api_client - INFO - 正在处理supplier 11/215: 100010
2025-09-19 15:14:53,594 - finale_api_client - INFO - 正在处理supplier 12/215: 100011
2025-09-19 15:14:53,961 - finale_api_client - INFO - 正在处理supplier 13/215: 100012
2025-09-19 15:14:54,322 - finale_api_client - INFO - 正在处理supplier 14/215: 100013
2025-09-19 15:14:54,735 - finale_api_client - INFO - 正在处理supplier 15/215: 100014
2025-09-19 15:14:55,128 - finale_api_client - INFO - 正在处理supplier 16/215: 100015
2025-09-19 15:14:55,480 - finale_api_client - INFO - 正在处理supplier 17/215: 100016
2025-09-19 15:14:55,829 - finale_api_client - INFO - 正在处理supplier 18/215: 100017
2025-09-19 15:14:56,142 - finale_api_client - INFO - 正在处理supplier 19/215: 100018
2025-09-19 15:16:05,657 - __main__ - INFO - 日志系统初始化完成
2025-09-19 15:16:05,657 - __main__ - INFO - 开始Finale Supplier数据迁移
2025-09-19 15:16:05,657 - __main__ - INFO - 验证连接...
2025-09-19 15:16:07,429 - __main__ - INFO - 所有连接验证成功
2025-09-19 15:16:07,430 - __main__ - INFO - 开始获取Finale supplier数据...
2025-09-19 15:16:07,430 - finale_api_client - INFO - 正在获取partygroup列表: https://app.finaleinventory.com/mercaso/api/partygroup
2025-09-19 15:16:07,860 - finale_api_client - INFO - 成功获取partygroup列表，共 215 个supplier
2025-09-19 15:16:07,860 - finale_api_client - INFO - 正在处理supplier 1/215: 100000
2025-09-19 15:16:08,212 - finale_api_client - INFO - 正在处理supplier 2/215: 100001
2025-09-19 15:16:08,557 - finale_api_client - INFO - 正在处理supplier 3/215: 100002
2025-09-19 15:16:08,947 - finale_api_client - INFO - 正在处理supplier 4/215: 100003
2025-09-19 15:16:09,316 - finale_api_client - INFO - 正在处理supplier 5/215: 100004
2025-09-19 15:16:09,659 - finale_api_client - INFO - 正在处理supplier 6/215: 100005
2025-09-19 15:16:10,007 - finale_api_client - INFO - 正在处理supplier 7/215: 100006
2025-09-19 15:16:10,366 - finale_api_client - INFO - 正在处理supplier 8/215: 100007
2025-09-19 15:16:10,703 - finale_api_client - INFO - 正在处理supplier 9/215: 100008
2025-09-19 15:16:11,094 - finale_api_client - INFO - 正在处理supplier 10/215: 100009
2025-09-19 15:16:11,491 - finale_api_client - INFO - 正在处理supplier 11/215: 100010
2025-09-19 15:16:11,855 - finale_api_client - INFO - 正在处理supplier 12/215: 100011
2025-09-19 15:16:12,234 - finale_api_client - INFO - 正在处理supplier 13/215: 100012
2025-09-19 15:16:12,653 - finale_api_client - INFO - 正在处理supplier 14/215: 100013
2025-09-19 15:16:13,032 - finale_api_client - INFO - 正在处理supplier 15/215: 100014
2025-09-19 15:16:13,459 - finale_api_client - INFO - 正在处理supplier 16/215: 100015
2025-09-19 15:16:13,828 - finale_api_client - INFO - 正在处理supplier 17/215: 100016
2025-09-19 15:16:14,153 - finale_api_client - INFO - 正在处理supplier 18/215: 100017
2025-09-19 15:16:14,473 - finale_api_client - INFO - 正在处理supplier 19/215: 100018
2025-09-19 15:16:14,825 - finale_api_client - INFO - 正在处理supplier 20/215: 100019
2025-09-19 15:16:15,181 - finale_api_client - INFO - 正在处理supplier 21/215: 100020
2025-09-19 15:16:15,519 - finale_api_client - INFO - 正在处理supplier 22/215: 100021
2025-09-19 15:16:15,909 - finale_api_client - INFO - 正在处理supplier 23/215: 100022
2025-09-19 15:16:16,257 - finale_api_client - INFO - 正在处理supplier 24/215: 100023
2025-09-19 15:16:16,627 - finale_api_client - INFO - 正在处理supplier 25/215: 100024
2025-09-19 15:16:17,603 - finale_api_client - INFO - 正在处理supplier 26/215: 100025
2025-09-19 15:16:17,970 - finale_api_client - INFO - 正在处理supplier 27/215: 100026
2025-09-19 15:16:18,311 - finale_api_client - INFO - 正在处理supplier 28/215: 100027
2025-09-19 15:16:18,649 - finale_api_client - INFO - 正在处理supplier 29/215: 100028
2025-09-19 15:16:18,969 - finale_api_client - INFO - 正在处理supplier 30/215: 100029
2025-09-19 15:16:19,402 - finale_api_client - INFO - 正在处理supplier 31/215: 100030
2025-09-19 15:16:19,737 - finale_api_client - INFO - 正在处理supplier 32/215: 100031
2025-09-19 15:16:20,079 - finale_api_client - INFO - 正在处理supplier 33/215: 100032
2025-09-19 15:16:20,522 - finale_api_client - INFO - 正在处理supplier 34/215: 100033
2025-09-19 15:16:20,930 - finale_api_client - INFO - 正在处理supplier 35/215: 100034
2025-09-19 15:16:21,322 - finale_api_client - INFO - 正在处理supplier 36/215: 100035
2025-09-19 15:16:21,729 - finale_api_client - INFO - 正在处理supplier 37/215: 100036
2025-09-19 15:16:22,098 - finale_api_client - INFO - 正在处理supplier 38/215: 100037
2025-09-19 15:16:22,469 - finale_api_client - INFO - 正在处理supplier 39/215: 100038
2025-09-19 15:16:22,801 - finale_api_client - INFO - 正在处理supplier 40/215: 100039
2025-09-19 15:16:23,197 - finale_api_client - INFO - 正在处理supplier 41/215: 100040
2025-09-19 15:16:23,568 - finale_api_client - INFO - 正在处理supplier 42/215: 100041
2025-09-19 15:16:24,021 - finale_api_client - INFO - 正在处理supplier 43/215: 100042
2025-09-19 15:16:24,411 - finale_api_client - INFO - 正在处理supplier 44/215: 100043
2025-09-19 15:16:24,757 - finale_api_client - INFO - 正在处理supplier 45/215: 100044
2025-09-19 15:16:25,099 - finale_api_client - INFO - 正在处理supplier 46/215: 100045
2025-09-19 15:16:25,468 - finale_api_client - INFO - 正在处理supplier 47/215: 100046
2025-09-19 15:16:25,888 - finale_api_client - INFO - 正在处理supplier 48/215: 100047
2025-09-19 15:16:26,207 - finale_api_client - INFO - 正在处理supplier 49/215: 100048
2025-09-19 15:16:26,558 - finale_api_client - INFO - 正在处理supplier 50/215: 100049
2025-09-19 15:16:26,875 - finale_api_client - INFO - 正在处理supplier 51/215: 100050
2025-09-19 15:16:27,291 - finale_api_client - INFO - 正在处理supplier 52/215: 100051
2025-09-19 15:16:27,654 - finale_api_client - INFO - 正在处理supplier 53/215: 100052
2025-09-19 15:16:28,016 - finale_api_client - INFO - 正在处理supplier 54/215: 100053
2025-09-19 15:16:28,338 - finale_api_client - INFO - 正在处理supplier 55/215: 100054
2025-09-19 15:16:28,658 - finale_api_client - INFO - 正在处理supplier 56/215: 100055
2025-09-19 15:16:29,005 - finale_api_client - INFO - 正在处理supplier 57/215: 100056
2025-09-19 15:16:29,342 - finale_api_client - INFO - 正在处理supplier 58/215: 100057
2025-09-19 15:16:29,680 - finale_api_client - INFO - 正在处理supplier 59/215: 100058
2025-09-19 15:16:30,001 - finale_api_client - INFO - 正在处理supplier 60/215: 100059
2025-09-19 15:16:30,330 - finale_api_client - INFO - 正在处理supplier 61/215: 100060
2025-09-19 15:16:30,731 - finale_api_client - INFO - 正在处理supplier 62/215: 100061
2025-09-19 15:16:31,050 - finale_api_client - INFO - 正在处理supplier 63/215: 100062
2025-09-19 15:16:31,381 - finale_api_client - INFO - 正在处理supplier 64/215: 100063
2025-09-19 15:16:31,727 - finale_api_client - INFO - 正在处理supplier 65/215: 100064
2025-09-19 15:16:32,118 - finale_api_client - INFO - 正在处理supplier 66/215: 100065
2025-09-19 15:16:32,506 - finale_api_client - INFO - 正在处理supplier 67/215: 100066
2025-09-19 15:16:32,866 - finale_api_client - INFO - 正在处理supplier 68/215: 100067
2025-09-19 15:16:33,228 - finale_api_client - INFO - 正在处理supplier 69/215: 100068
2025-09-19 15:16:33,579 - finale_api_client - INFO - 正在处理supplier 70/215: 100069
2025-09-19 15:16:33,928 - finale_api_client - INFO - 正在处理supplier 71/215: 100070
2025-09-19 15:16:34,248 - finale_api_client - INFO - 正在处理supplier 72/215: 100071
2025-09-19 15:16:34,595 - finale_api_client - INFO - 正在处理supplier 73/215: 100072
2025-09-19 15:16:34,920 - finale_api_client - INFO - 正在处理supplier 74/215: 100073
2025-09-19 15:16:35,343 - finale_api_client - INFO - 正在处理supplier 75/215: 100074
2025-09-19 15:16:35,774 - finale_api_client - INFO - 正在处理supplier 76/215: 100075
2025-09-19 15:16:36,114 - finale_api_client - INFO - 正在处理supplier 77/215: 100076
2025-09-19 15:16:36,514 - finale_api_client - INFO - 正在处理supplier 78/215: 100077
2025-09-19 15:16:36,912 - finale_api_client - INFO - 正在处理supplier 79/215: 100078
2025-09-19 15:16:37,249 - finale_api_client - INFO - 正在处理supplier 80/215: 100079
2025-09-19 15:16:37,627 - finale_api_client - INFO - 正在处理supplier 81/215: 100080
2025-09-19 15:16:37,952 - finale_api_client - INFO - 正在处理supplier 82/215: 100081
2025-09-19 15:16:38,339 - finale_api_client - INFO - 正在处理supplier 83/215: 100082
2025-09-19 15:16:38,748 - finale_api_client - INFO - 正在处理supplier 84/215: 100083
2025-09-19 15:16:39,156 - finale_api_client - INFO - 正在处理supplier 85/215: 100084
2025-09-19 15:16:39,521 - finale_api_client - INFO - 正在处理supplier 86/215: 100085
2025-09-19 15:16:39,882 - finale_api_client - INFO - 正在处理supplier 87/215: 100086
2025-09-19 15:16:40,232 - finale_api_client - INFO - 正在处理supplier 88/215: 100087
2025-09-19 15:16:40,555 - finale_api_client - INFO - 正在处理supplier 89/215: 100088
2025-09-19 15:16:40,872 - finale_api_client - INFO - 正在处理supplier 90/215: 100089
2025-09-19 15:16:41,205 - finale_api_client - INFO - 正在处理supplier 91/215: 100090
2025-09-19 15:16:41,531 - finale_api_client - INFO - 正在处理supplier 92/215: 100091
2025-09-19 15:16:41,913 - finale_api_client - INFO - 正在处理supplier 93/215: 100092
2025-09-19 15:16:42,255 - finale_api_client - INFO - 正在处理supplier 94/215: 100093
2025-09-19 15:16:42,646 - finale_api_client - INFO - 正在处理supplier 95/215: 100094
2025-09-19 15:16:43,048 - finale_api_client - INFO - 正在处理supplier 96/215: 100095
2025-09-19 15:16:43,378 - finale_api_client - INFO - 正在处理supplier 97/215: 100096
2025-09-19 15:16:43,761 - finale_api_client - INFO - 正在处理supplier 98/215: 100097
2025-09-19 15:16:44,304 - finale_api_client - INFO - 正在处理supplier 99/215: 100098
2025-09-19 15:16:45,724 - finale_api_client - INFO - 正在处理supplier 100/215: 100099
2025-09-19 15:16:46,076 - finale_api_client - INFO - 正在处理supplier 101/215: 100100
2025-09-19 15:16:46,424 - finale_api_client - INFO - 正在处理supplier 102/215: 100101
2025-09-19 15:16:46,836 - finale_api_client - INFO - 正在处理supplier 103/215: 100102
2025-09-19 15:16:47,243 - finale_api_client - INFO - 正在处理supplier 104/215: 100103
2025-09-19 15:16:47,593 - finale_api_client - INFO - 正在处理supplier 105/215: 100104
2025-09-19 15:16:47,939 - finale_api_client - INFO - 正在处理supplier 106/215: 100105
2025-09-19 15:16:48,310 - finale_api_client - INFO - 正在处理supplier 107/215: 100106
2025-09-19 15:16:48,656 - finale_api_client - INFO - 正在处理supplier 108/215: 100107
2025-09-19 15:16:48,989 - finale_api_client - INFO - 正在处理supplier 109/215: 100108
2025-09-19 15:16:49,331 - finale_api_client - INFO - 正在处理supplier 110/215: 100109
2025-09-19 15:16:49,753 - finale_api_client - INFO - 正在处理supplier 111/215: 100110
2025-09-19 15:16:50,098 - finale_api_client - INFO - 正在处理supplier 112/215: 100111
2025-09-19 15:16:50,631 - finale_api_client - INFO - 正在处理supplier 113/215: 100112
2025-09-19 15:16:50,982 - finale_api_client - INFO - 正在处理supplier 114/215: 100113
2025-09-19 15:16:51,327 - finale_api_client - INFO - 正在处理supplier 115/215: 100114
2025-09-19 15:16:51,677 - finale_api_client - INFO - 正在处理supplier 116/215: 100115
2025-09-19 15:16:51,999 - finale_api_client - INFO - 正在处理supplier 117/215: 100116
2025-09-19 15:16:52,329 - finale_api_client - INFO - 正在处理supplier 118/215: 100117
2025-09-19 15:16:52,648 - finale_api_client - INFO - 正在处理supplier 119/215: 100118
2025-09-19 15:16:52,981 - finale_api_client - INFO - 正在处理supplier 120/215: 100119
2025-09-19 15:16:53,399 - finale_api_client - INFO - 正在处理supplier 121/215: 100120
2025-09-19 15:16:53,809 - finale_api_client - INFO - 正在处理supplier 122/215: 100121
2025-09-19 15:16:54,209 - finale_api_client - INFO - 正在处理supplier 123/215: 100122
2025-09-19 15:16:54,539 - finale_api_client - INFO - 正在处理supplier 124/215: 100123
2025-09-19 15:16:54,858 - finale_api_client - INFO - 正在处理supplier 125/215: 100124
2025-09-19 15:16:55,219 - finale_api_client - INFO - 正在处理supplier 126/215: 100125
2025-09-19 15:16:55,652 - finale_api_client - INFO - 正在处理supplier 127/215: 100126
2025-09-19 15:16:56,169 - finale_api_client - INFO - 正在处理supplier 128/215: 100127
2025-09-19 15:16:56,813 - finale_api_client - INFO - 正在处理supplier 129/215: 100128
2025-09-19 15:16:57,687 - finale_api_client - INFO - 正在处理supplier 130/215: 100129
2025-09-19 15:16:58,373 - finale_api_client - INFO - 正在处理supplier 131/215: 100130
2025-09-19 15:16:59,009 - finale_api_client - INFO - 正在处理supplier 132/215: 100131
2025-09-19 15:16:59,560 - finale_api_client - INFO - 正在处理supplier 133/215: 100132
2025-09-19 15:16:59,877 - finale_api_client - INFO - 正在处理supplier 134/215: 100133
2025-09-19 15:17:00,205 - finale_api_client - INFO - 正在处理supplier 135/215: 100134
2025-09-19 15:17:00,550 - finale_api_client - INFO - 正在处理supplier 136/215: 100135
2025-09-19 15:17:00,893 - finale_api_client - INFO - 正在处理supplier 137/215: 100136
2025-09-19 15:17:01,217 - finale_api_client - INFO - 正在处理supplier 138/215: 100137
2025-09-19 15:17:01,601 - finale_api_client - INFO - 正在处理supplier 139/215: 100138
2025-09-19 15:17:01,952 - finale_api_client - INFO - 正在处理supplier 140/215: 100139
2025-09-19 15:17:02,307 - finale_api_client - INFO - 正在处理supplier 141/215: 100140
2025-09-19 15:17:02,713 - finale_api_client - INFO - 正在处理supplier 142/215: 100141
2025-09-19 15:17:03,042 - finale_api_client - INFO - 正在处理supplier 143/215: 100142
2025-09-19 15:17:03,362 - finale_api_client - INFO - 正在处理supplier 144/215: 100143
2025-09-19 15:17:03,694 - finale_api_client - INFO - 正在处理supplier 145/215: 100144
2025-09-19 15:17:04,015 - finale_api_client - INFO - 正在处理supplier 146/215: 100145
2025-09-19 15:17:04,350 - finale_api_client - INFO - 正在处理supplier 147/215: 100146
2025-09-19 15:17:04,743 - finale_api_client - INFO - 正在处理supplier 148/215: 100147
2025-09-19 15:17:05,103 - finale_api_client - INFO - 正在处理supplier 149/215: 100148
2025-09-19 15:17:05,432 - finale_api_client - INFO - 正在处理supplier 150/215: 100149
2025-09-19 15:17:05,776 - finale_api_client - INFO - 正在处理supplier 151/215: 100150
2025-09-19 15:17:06,192 - finale_api_client - INFO - 正在处理supplier 152/215: 100151
2025-09-19 15:17:06,559 - finale_api_client - INFO - 正在处理supplier 153/215: 100152
2025-09-19 15:17:06,898 - finale_api_client - INFO - 正在处理supplier 154/215: 100153
2025-09-19 15:17:07,241 - finale_api_client - INFO - 正在处理supplier 155/215: 100154
2025-09-19 15:17:07,561 - finale_api_client - INFO - 正在处理supplier 156/215: 100155
2025-09-19 15:17:07,917 - finale_api_client - INFO - 正在处理supplier 157/215: 100156
2025-09-19 15:17:08,313 - finale_api_client - INFO - 正在处理supplier 158/215: 100157
2025-09-19 15:17:08,664 - finale_api_client - INFO - 正在处理supplier 159/215: 100158
2025-09-19 15:17:09,011 - finale_api_client - INFO - 正在处理supplier 160/215: 100159
2025-09-19 15:17:09,358 - finale_api_client - INFO - 正在处理supplier 161/215: 100160
2025-09-19 15:17:09,717 - finale_api_client - INFO - 正在处理supplier 162/215: 100161
2025-09-19 15:17:10,067 - finale_api_client - INFO - 正在处理supplier 163/215: 100162
2025-09-19 15:17:10,392 - finale_api_client - INFO - 正在处理supplier 164/215: 100163
2025-09-19 15:17:10,800 - finale_api_client - INFO - 正在处理supplier 165/215: 100164
2025-09-19 15:17:11,154 - finale_api_client - INFO - 正在处理supplier 166/215: 100165
2025-09-19 15:17:11,528 - finale_api_client - INFO - 正在处理supplier 167/215: 100166
2025-09-19 15:17:11,881 - finale_api_client - INFO - 正在处理supplier 168/215: 100167
2025-09-19 15:17:12,230 - finale_api_client - INFO - 正在处理supplier 169/215: 100168
2025-09-19 15:17:12,641 - finale_api_client - INFO - 正在处理supplier 170/215: 100169
2025-09-19 15:17:12,962 - finale_api_client - INFO - 正在处理supplier 171/215: 100170
2025-09-19 15:17:13,360 - finale_api_client - INFO - 正在处理supplier 172/215: 100171
2025-09-19 15:17:13,691 - finale_api_client - INFO - 正在处理supplier 173/215: 100172
2025-09-19 15:17:14,030 - finale_api_client - INFO - 正在处理supplier 174/215: 100173
2025-09-19 15:17:14,402 - finale_api_client - INFO - 正在处理supplier 175/215: 100174
2025-09-19 15:17:14,744 - finale_api_client - INFO - 正在处理supplier 176/215: 100175
2025-09-19 15:17:15,083 - finale_api_client - INFO - 正在处理supplier 177/215: 100176
2025-09-19 15:17:15,433 - finale_api_client - INFO - 正在处理supplier 178/215: 100177
2025-09-19 15:17:15,779 - finale_api_client - INFO - 正在处理supplier 179/215: 100178
2025-09-19 15:17:16,162 - finale_api_client - INFO - 正在处理supplier 180/215: 100179
2025-09-19 15:17:16,505 - finale_api_client - INFO - 正在处理supplier 181/215: 100180
2025-09-19 15:17:16,856 - finale_api_client - INFO - 正在处理supplier 182/215: 100181
2025-09-19 15:17:17,206 - finale_api_client - INFO - 正在处理supplier 183/215: 100182
2025-09-19 15:17:17,547 - finale_api_client - INFO - 正在处理supplier 184/215: 100183
2025-09-19 15:17:17,915 - finale_api_client - INFO - 正在处理supplier 185/215: 100184
2025-09-19 15:17:18,256 - finale_api_client - INFO - 正在处理supplier 186/215: 100185
2025-09-19 15:17:18,598 - finale_api_client - INFO - 正在处理supplier 187/215: 100186
2025-09-19 15:17:18,932 - finale_api_client - INFO - 正在处理supplier 188/215: 100187
2025-09-19 15:17:19,274 - finale_api_client - INFO - 正在处理supplier 189/215: 100188
2025-09-19 15:17:19,612 - finale_api_client - INFO - 正在处理supplier 190/215: 100189
2025-09-19 15:17:19,970 - finale_api_client - INFO - 正在处理supplier 191/215: 100190
2025-09-19 15:17:20,314 - finale_api_client - INFO - 正在处理supplier 192/215: 100191
2025-09-19 15:17:20,692 - finale_api_client - INFO - 正在处理supplier 193/215: 100192
2025-09-19 15:17:21,040 - finale_api_client - INFO - 正在处理supplier 194/215: 100193
2025-09-19 15:17:21,383 - finale_api_client - INFO - 正在处理supplier 195/215: 100194
2025-09-19 15:17:21,716 - finale_api_client - INFO - 正在处理supplier 196/215: 100195
2025-09-19 15:17:22,033 - finale_api_client - INFO - 正在处理supplier 197/215: 100196
2025-09-19 15:17:22,359 - finale_api_client - INFO - 正在处理supplier 198/215: 100197
2025-09-19 15:17:22,784 - finale_api_client - INFO - 正在处理supplier 199/215: 100198
2025-09-19 15:17:24,314 - finale_api_client - INFO - 正在处理supplier 200/215: 100199
2025-09-19 15:17:24,726 - finale_api_client - INFO - 正在处理supplier 201/215: 100200
2025-09-19 15:17:25,097 - finale_api_client - INFO - 正在处理supplier 202/215: 100201
2025-09-19 15:17:25,492 - finale_api_client - INFO - 正在处理supplier 203/215: 100202
2025-09-19 15:17:25,805 - finale_api_client - INFO - 正在处理supplier 204/215: 100203
2025-09-19 15:17:26,128 - finale_api_client - INFO - 正在处理supplier 205/215: 100204
2025-09-19 15:17:26,557 - finale_api_client - INFO - 正在处理supplier 206/215: 100205
2025-09-19 15:17:26,976 - finale_api_client - INFO - 正在处理supplier 207/215: 100206
2025-09-19 15:17:27,354 - finale_api_client - INFO - 正在处理supplier 208/215: 100207
2025-09-19 15:17:27,688 - finale_api_client - INFO - 正在处理supplier 209/215: 100208
2025-09-19 15:17:28,013 - finale_api_client - INFO - 正在处理supplier 210/215: 100209
2025-09-19 15:17:28,400 - finale_api_client - INFO - 正在处理supplier 211/215: 100210
2025-09-19 15:17:28,751 - finale_api_client - INFO - 正在处理supplier 212/215: 100211
2025-09-19 15:17:29,091 - finale_api_client - INFO - 正在处理supplier 213/215: 100212
2025-09-19 15:17:29,433 - finale_api_client - INFO - 正在处理supplier 214/215: 100213
2025-09-19 15:17:29,774 - finale_api_client - INFO - 正在处理supplier 215/215: PRIMARY
2025-09-19 15:17:30,374 - finale_api_client - INFO - 成功获取 215 个supplier的完整数据
2025-09-19 15:17:30,375 - __main__ - INFO - 成功获取 215 个supplier数据
2025-09-19 15:17:30,376 - __main__ - INFO - 开始处理 215 个supplier...
2025-09-19 15:17:30,376 - __main__ - INFO - 进度: 1/215
2025-09-19 15:17:30,376 - __main__ - INFO - 处理supplier: 100000
2025-09-19 15:17:30,388 - __main__ - WARNING - 未找到对应的vendor，跳过: 100000
2025-09-19 15:17:30,389 - __main__ - INFO - 进度: 2/215
2025-09-19 15:17:30,389 - __main__ - INFO - 处理supplier: 100001
2025-09-19 15:17:30,392 - __main__ - WARNING - 未找到对应的vendor，跳过: 100001
2025-09-19 15:17:30,393 - __main__ - INFO - 进度: 3/215
2025-09-19 15:17:30,393 - __main__ - INFO - 处理supplier: 100002
2025-09-19 15:17:30,396 - __main__ - WARNING - 未找到对应的vendor，跳过: 100002
2025-09-19 15:17:30,396 - __main__ - INFO - 进度: 4/215
2025-09-19 15:17:30,396 - __main__ - INFO - 处理supplier: 100003
2025-09-19 15:17:30,403 - __main__ - INFO - 找到对应vendor: 36222a07-b492-4dfb-a770-d17c44800f6e
2025-09-19 15:17:30,403 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,403 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,410 - database_manager - INFO - 成功更新vendor: 36222a07-b492-4dfb-a770-d17c44800f6e
2025-09-19 15:17:30,414 - __main__ - INFO - 成功更新vendor: 36222a07-b492-4dfb-a770-d17c44800f6e
2025-09-19 15:17:30,432 - database_manager - INFO - 成功删除vendor联系方式: 36222a07-b492-4dfb-a770-d17c44800f6e
2025-09-19 15:17:30,446 - __main__ - INFO - 成功处理supplier: 100003
2025-09-19 15:17:30,446 - __main__ - INFO - 进度: 5/215
2025-09-19 15:17:30,447 - __main__ - INFO - 处理supplier: 100004
2025-09-19 15:17:30,452 - __main__ - INFO - 找到对应vendor: b3dc7e87-075a-4cf6-8ba2-0f0835918efa
2025-09-19 15:17:30,452 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,452 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,452 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,458 - database_manager - INFO - 成功更新vendor: b3dc7e87-075a-4cf6-8ba2-0f0835918efa
2025-09-19 15:17:30,459 - __main__ - INFO - 成功更新vendor: b3dc7e87-075a-4cf6-8ba2-0f0835918efa
2025-09-19 15:17:30,466 - database_manager - INFO - 成功删除vendor联系方式: b3dc7e87-075a-4cf6-8ba2-0f0835918efa
2025-09-19 15:17:30,478 - __main__ - INFO - 成功处理supplier: 100004
2025-09-19 15:17:30,478 - __main__ - INFO - 进度: 6/215
2025-09-19 15:17:30,478 - __main__ - INFO - 处理supplier: 100005
2025-09-19 15:17:30,481 - __main__ - INFO - 找到对应vendor: 6bacaf0d-4809-468e-8f6e-8bf3ef0f823d
2025-09-19 15:17:30,481 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,481 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,484 - database_manager - INFO - 成功更新vendor: 6bacaf0d-4809-468e-8f6e-8bf3ef0f823d
2025-09-19 15:17:30,485 - __main__ - INFO - 成功更新vendor: 6bacaf0d-4809-468e-8f6e-8bf3ef0f823d
2025-09-19 15:17:30,491 - database_manager - INFO - 成功删除vendor联系方式: 6bacaf0d-4809-468e-8f6e-8bf3ef0f823d
2025-09-19 15:17:30,497 - __main__ - INFO - 成功处理supplier: 100005
2025-09-19 15:17:30,498 - __main__ - INFO - 进度: 7/215
2025-09-19 15:17:30,498 - __main__ - INFO - 处理supplier: 100006
2025-09-19 15:17:30,502 - __main__ - INFO - 找到对应vendor: 7588850f-44f4-42fb-919a-db6a4ebdda8e
2025-09-19 15:17:30,502 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,502 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,502 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,504 - database_manager - INFO - 成功更新vendor: 7588850f-44f4-42fb-919a-db6a4ebdda8e
2025-09-19 15:17:30,507 - __main__ - INFO - 成功更新vendor: 7588850f-44f4-42fb-919a-db6a4ebdda8e
2025-09-19 15:17:30,512 - database_manager - INFO - 成功删除vendor联系方式: 7588850f-44f4-42fb-919a-db6a4ebdda8e
2025-09-19 15:17:30,519 - __main__ - INFO - 成功处理supplier: 100006
2025-09-19 15:17:30,519 - __main__ - INFO - 进度: 8/215
2025-09-19 15:17:30,519 - __main__ - INFO - 处理supplier: 100007
2025-09-19 15:17:30,521 - __main__ - INFO - 找到对应vendor: 46ae93fc-b12d-4e82-9809-65b2593d0e3e
2025-09-19 15:17:30,522 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,526 - database_manager - INFO - 成功更新vendor: 46ae93fc-b12d-4e82-9809-65b2593d0e3e
2025-09-19 15:17:30,527 - __main__ - INFO - 成功更新vendor: 46ae93fc-b12d-4e82-9809-65b2593d0e3e
2025-09-19 15:17:30,530 - database_manager - INFO - 成功删除vendor联系方式: 46ae93fc-b12d-4e82-9809-65b2593d0e3e
2025-09-19 15:17:30,534 - __main__ - INFO - 成功处理supplier: 100007
2025-09-19 15:17:30,534 - __main__ - INFO - 进度: 9/215
2025-09-19 15:17:30,534 - __main__ - INFO - 处理supplier: 100008
2025-09-19 15:17:30,536 - __main__ - INFO - 找到对应vendor: cf6a0a06-0aa3-42a9-bd7d-df30c93bd582
2025-09-19 15:17:30,536 - data_mapper - WARNING - 邮箱地址无效，跳过
2025-09-19 15:17:30,536 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,536 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,537 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,538 - database_manager - INFO - 成功更新vendor: cf6a0a06-0aa3-42a9-bd7d-df30c93bd582
2025-09-19 15:17:30,539 - __main__ - INFO - 成功更新vendor: cf6a0a06-0aa3-42a9-bd7d-df30c93bd582
2025-09-19 15:17:30,542 - database_manager - INFO - 成功删除vendor联系方式: cf6a0a06-0aa3-42a9-bd7d-df30c93bd582
2025-09-19 15:17:30,548 - __main__ - INFO - 成功处理supplier: 100008
2025-09-19 15:17:30,548 - __main__ - INFO - 进度: 10/215
2025-09-19 15:17:30,548 - __main__ - INFO - 处理supplier: 100009
2025-09-19 15:17:30,550 - __main__ - INFO - 找到对应vendor: 1bbde520-1750-4cb4-a785-03b4dbe672aa
2025-09-19 15:17:30,550 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,550 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,552 - database_manager - INFO - 成功更新vendor: 1bbde520-1750-4cb4-a785-03b4dbe672aa
2025-09-19 15:17:30,552 - __main__ - INFO - 成功更新vendor: 1bbde520-1750-4cb4-a785-03b4dbe672aa
2025-09-19 15:17:30,555 - database_manager - INFO - 成功删除vendor联系方式: 1bbde520-1750-4cb4-a785-03b4dbe672aa
2025-09-19 15:17:30,558 - __main__ - INFO - 成功处理supplier: 100009
2025-09-19 15:17:30,558 - __main__ - INFO - 进度: 11/215
2025-09-19 15:17:30,558 - __main__ - INFO - 处理supplier: 100010
2025-09-19 15:17:30,560 - __main__ - INFO - 找到对应vendor: da473beb-adcc-4a32-9b8d-bea8bdf6c741
2025-09-19 15:17:30,560 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,560 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,560 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,561 - database_manager - INFO - 成功更新vendor: da473beb-adcc-4a32-9b8d-bea8bdf6c741
2025-09-19 15:17:30,562 - __main__ - INFO - 成功更新vendor: da473beb-adcc-4a32-9b8d-bea8bdf6c741
2025-09-19 15:17:30,564 - database_manager - INFO - 成功删除vendor联系方式: da473beb-adcc-4a32-9b8d-bea8bdf6c741
2025-09-19 15:17:30,572 - __main__ - INFO - 成功处理supplier: 100010
2025-09-19 15:17:30,572 - __main__ - INFO - 进度: 12/215
2025-09-19 15:17:30,572 - __main__ - INFO - 处理supplier: 100011
2025-09-19 15:17:30,574 - __main__ - INFO - 找到对应vendor: 02ba9b84-46cd-41f3-8510-24956ed923a2
2025-09-19 15:17:30,574 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,574 - data_mapper - WARNING - 邮箱地址无效，跳过
2025-09-19 15:17:30,576 - database_manager - INFO - 成功更新vendor: 02ba9b84-46cd-41f3-8510-24956ed923a2
2025-09-19 15:17:30,576 - __main__ - INFO - 成功更新vendor: 02ba9b84-46cd-41f3-8510-24956ed923a2
2025-09-19 15:17:30,580 - database_manager - INFO - 成功删除vendor联系方式: 02ba9b84-46cd-41f3-8510-24956ed923a2
2025-09-19 15:17:30,589 - __main__ - INFO - 成功处理supplier: 100011
2025-09-19 15:17:30,590 - __main__ - INFO - 进度: 13/215
2025-09-19 15:17:30,590 - __main__ - INFO - 处理supplier: 100012
2025-09-19 15:17:30,593 - __main__ - INFO - 找到对应vendor: 9b1de203-c7c6-43b7-b558-94e0f1eaadee
2025-09-19 15:17:30,593 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,593 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,593 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,598 - database_manager - INFO - 成功更新vendor: 9b1de203-c7c6-43b7-b558-94e0f1eaadee
2025-09-19 15:17:30,600 - __main__ - INFO - 成功更新vendor: 9b1de203-c7c6-43b7-b558-94e0f1eaadee
2025-09-19 15:17:30,605 - database_manager - INFO - 成功删除vendor联系方式: 9b1de203-c7c6-43b7-b558-94e0f1eaadee
2025-09-19 15:17:30,609 - __main__ - INFO - 成功处理supplier: 100012
2025-09-19 15:17:30,609 - __main__ - INFO - 进度: 14/215
2025-09-19 15:17:30,609 - __main__ - INFO - 处理supplier: 100013
2025-09-19 15:17:30,611 - __main__ - INFO - 找到对应vendor: f568c5cb-1461-4129-a6a5-************
2025-09-19 15:17:30,611 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,611 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,611 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,612 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,614 - database_manager - INFO - 成功更新vendor: f568c5cb-1461-4129-a6a5-************
2025-09-19 15:17:30,616 - __main__ - INFO - 成功更新vendor: f568c5cb-1461-4129-a6a5-************
2025-09-19 15:17:30,619 - database_manager - INFO - 成功删除vendor联系方式: f568c5cb-1461-4129-a6a5-************
2025-09-19 15:17:30,625 - __main__ - INFO - 成功处理supplier: 100013
2025-09-19 15:17:30,625 - __main__ - INFO - 进度: 15/215
2025-09-19 15:17:30,625 - __main__ - INFO - 处理supplier: 100014
2025-09-19 15:17:30,626 - __main__ - INFO - 找到对应vendor: 4777608d-190a-43ba-a3bf-0a85761711a0
2025-09-19 15:17:30,626 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,626 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,627 - database_manager - INFO - 成功更新vendor: 4777608d-190a-43ba-a3bf-0a85761711a0
2025-09-19 15:17:30,628 - __main__ - INFO - 成功更新vendor: 4777608d-190a-43ba-a3bf-0a85761711a0
2025-09-19 15:17:30,631 - database_manager - INFO - 成功删除vendor联系方式: 4777608d-190a-43ba-a3bf-0a85761711a0
2025-09-19 15:17:30,636 - __main__ - INFO - 成功处理supplier: 100014
2025-09-19 15:17:30,636 - __main__ - INFO - 进度: 16/215
2025-09-19 15:17:30,636 - __main__ - INFO - 处理supplier: 100015
2025-09-19 15:17:30,637 - __main__ - INFO - 找到对应vendor: 469a13db-9d53-44a9-9a8e-bb87af0e1fef
2025-09-19 15:17:30,638 - database_manager - INFO - 成功更新vendor: 469a13db-9d53-44a9-9a8e-bb87af0e1fef
2025-09-19 15:17:30,638 - __main__ - INFO - 成功更新vendor: 469a13db-9d53-44a9-9a8e-bb87af0e1fef
2025-09-19 15:17:30,640 - database_manager - INFO - 成功删除vendor联系方式: 469a13db-9d53-44a9-9a8e-bb87af0e1fef
2025-09-19 15:17:30,641 - __main__ - INFO - 成功处理supplier: 100015
2025-09-19 15:17:30,641 - __main__ - INFO - 进度: 17/215
2025-09-19 15:17:30,641 - __main__ - INFO - 处理supplier: 100016
2025-09-19 15:17:30,642 - __main__ - INFO - 找到对应vendor: cac2da56-067f-48f7-9a3a-b626cf2e3ccb
2025-09-19 15:17:30,643 - database_manager - INFO - 成功更新vendor: cac2da56-067f-48f7-9a3a-b626cf2e3ccb
2025-09-19 15:17:30,643 - __main__ - INFO - 成功更新vendor: cac2da56-067f-48f7-9a3a-b626cf2e3ccb
2025-09-19 15:17:30,645 - database_manager - INFO - 成功删除vendor联系方式: cac2da56-067f-48f7-9a3a-b626cf2e3ccb
2025-09-19 15:17:30,646 - __main__ - INFO - 成功处理supplier: 100016
2025-09-19 15:17:30,646 - __main__ - INFO - 进度: 18/215
2025-09-19 15:17:30,646 - __main__ - INFO - 处理supplier: 100017
2025-09-19 15:17:30,647 - __main__ - WARNING - 未找到对应的vendor，跳过: 100017
2025-09-19 15:17:30,647 - __main__ - INFO - 进度: 19/215
2025-09-19 15:17:30,647 - __main__ - INFO - 处理supplier: 100018
2025-09-19 15:17:30,648 - __main__ - INFO - 找到对应vendor: b72fc2f8-7579-4793-8e11-6dd027cf7cbd
2025-09-19 15:17:30,648 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,650 - database_manager - INFO - 成功更新vendor: b72fc2f8-7579-4793-8e11-6dd027cf7cbd
2025-09-19 15:17:30,650 - __main__ - INFO - 成功更新vendor: b72fc2f8-7579-4793-8e11-6dd027cf7cbd
2025-09-19 15:17:30,652 - database_manager - INFO - 成功删除vendor联系方式: b72fc2f8-7579-4793-8e11-6dd027cf7cbd
2025-09-19 15:17:30,654 - __main__ - INFO - 成功处理supplier: 100018
2025-09-19 15:17:30,654 - __main__ - INFO - 进度: 20/215
2025-09-19 15:17:30,654 - __main__ - INFO - 处理supplier: 100019
2025-09-19 15:17:30,655 - __main__ - INFO - 找到对应vendor: a76a26d4-7597-49af-8b55-23fdbd5dc8d2
2025-09-19 15:17:30,655 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,655 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,656 - database_manager - INFO - 成功更新vendor: a76a26d4-7597-49af-8b55-23fdbd5dc8d2
2025-09-19 15:17:30,656 - __main__ - INFO - 成功更新vendor: a76a26d4-7597-49af-8b55-23fdbd5dc8d2
2025-09-19 15:17:30,658 - database_manager - INFO - 成功删除vendor联系方式: a76a26d4-7597-49af-8b55-23fdbd5dc8d2
2025-09-19 15:17:30,659 - __main__ - INFO - 成功处理supplier: 100019
2025-09-19 15:17:30,659 - __main__ - INFO - 进度: 21/215
2025-09-19 15:17:30,659 - __main__ - INFO - 处理supplier: 100020
2025-09-19 15:17:30,660 - __main__ - INFO - 找到对应vendor: 4c7dc747-4d13-47f8-8499-65acb0c8a71c
2025-09-19 15:17:30,660 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,660 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,662 - database_manager - INFO - 成功更新vendor: 4c7dc747-4d13-47f8-8499-65acb0c8a71c
2025-09-19 15:17:30,662 - __main__ - INFO - 成功更新vendor: 4c7dc747-4d13-47f8-8499-65acb0c8a71c
2025-09-19 15:17:30,665 - database_manager - INFO - 成功删除vendor联系方式: 4c7dc747-4d13-47f8-8499-65acb0c8a71c
2025-09-19 15:17:30,668 - __main__ - INFO - 成功处理supplier: 100020
2025-09-19 15:17:30,668 - __main__ - INFO - 进度: 22/215
2025-09-19 15:17:30,668 - __main__ - INFO - 处理supplier: 100021
2025-09-19 15:17:30,669 - __main__ - WARNING - 未找到对应的vendor，跳过: 100021
2025-09-19 15:17:30,669 - __main__ - INFO - 进度: 23/215
2025-09-19 15:17:30,669 - __main__ - INFO - 处理supplier: 100022
2025-09-19 15:17:30,670 - __main__ - INFO - 找到对应vendor: 2c19c67d-cf3d-482a-b407-30b10b45c9b0
2025-09-19 15:17:30,670 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,670 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,671 - database_manager - INFO - 成功更新vendor: 2c19c67d-cf3d-482a-b407-30b10b45c9b0
2025-09-19 15:17:30,671 - __main__ - INFO - 成功更新vendor: 2c19c67d-cf3d-482a-b407-30b10b45c9b0
2025-09-19 15:17:30,673 - database_manager - INFO - 成功删除vendor联系方式: 2c19c67d-cf3d-482a-b407-30b10b45c9b0
2025-09-19 15:17:30,675 - __main__ - INFO - 成功处理supplier: 100022
2025-09-19 15:17:30,675 - __main__ - INFO - 进度: 24/215
2025-09-19 15:17:30,675 - __main__ - INFO - 处理supplier: 100023
2025-09-19 15:17:30,676 - __main__ - INFO - 找到对应vendor: 1496b3f0-841f-4bad-b7b4-82363d8b89ae
2025-09-19 15:17:30,676 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,676 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,677 - database_manager - INFO - 成功更新vendor: 1496b3f0-841f-4bad-b7b4-82363d8b89ae
2025-09-19 15:17:30,677 - __main__ - INFO - 成功更新vendor: 1496b3f0-841f-4bad-b7b4-82363d8b89ae
2025-09-19 15:17:30,679 - database_manager - INFO - 成功删除vendor联系方式: 1496b3f0-841f-4bad-b7b4-82363d8b89ae
2025-09-19 15:17:30,682 - __main__ - INFO - 成功处理supplier: 100023
2025-09-19 15:17:30,682 - __main__ - INFO - 进度: 25/215
2025-09-19 15:17:30,682 - __main__ - INFO - 处理supplier: 100024
2025-09-19 15:17:30,683 - __main__ - WARNING - 未找到对应的vendor，跳过: 100024
2025-09-19 15:17:30,683 - __main__ - INFO - 进度: 26/215
2025-09-19 15:17:30,683 - __main__ - INFO - 处理supplier: 100025
2025-09-19 15:17:30,684 - __main__ - INFO - 找到对应vendor: 4b2e8574-f674-46ec-979e-a1f6d35ce261
2025-09-19 15:17:30,685 - database_manager - INFO - 成功更新vendor: 4b2e8574-f674-46ec-979e-a1f6d35ce261
2025-09-19 15:17:30,686 - __main__ - INFO - 成功更新vendor: 4b2e8574-f674-46ec-979e-a1f6d35ce261
2025-09-19 15:17:30,687 - database_manager - INFO - 成功删除vendor联系方式: 4b2e8574-f674-46ec-979e-a1f6d35ce261
2025-09-19 15:17:30,691 - __main__ - INFO - 成功处理supplier: 100025
2025-09-19 15:17:30,691 - __main__ - INFO - 进度: 27/215
2025-09-19 15:17:30,691 - __main__ - INFO - 处理supplier: 100026
2025-09-19 15:17:30,692 - __main__ - WARNING - 未找到对应的vendor，跳过: 100026
2025-09-19 15:17:30,692 - __main__ - INFO - 进度: 28/215
2025-09-19 15:17:30,692 - __main__ - INFO - 处理supplier: 100027
2025-09-19 15:17:30,693 - __main__ - INFO - 找到对应vendor: 2d040d89-3a89-4f0a-a74d-7dba1452b67c
2025-09-19 15:17:30,693 - database_manager - INFO - 成功更新vendor: 2d040d89-3a89-4f0a-a74d-7dba1452b67c
2025-09-19 15:17:30,694 - __main__ - INFO - 成功更新vendor: 2d040d89-3a89-4f0a-a74d-7dba1452b67c
2025-09-19 15:17:30,695 - database_manager - INFO - 成功删除vendor联系方式: 2d040d89-3a89-4f0a-a74d-7dba1452b67c
2025-09-19 15:17:30,696 - __main__ - INFO - 成功处理supplier: 100027
2025-09-19 15:17:30,696 - __main__ - INFO - 进度: 29/215
2025-09-19 15:17:30,696 - __main__ - INFO - 处理supplier: 100028
2025-09-19 15:17:30,697 - __main__ - INFO - 找到对应vendor: db23b3e1-dd8f-4c48-b9df-4afde6e2db70
2025-09-19 15:17:30,698 - database_manager - INFO - 成功更新vendor: db23b3e1-dd8f-4c48-b9df-4afde6e2db70
2025-09-19 15:17:30,699 - __main__ - INFO - 成功更新vendor: db23b3e1-dd8f-4c48-b9df-4afde6e2db70
2025-09-19 15:17:30,701 - database_manager - INFO - 成功删除vendor联系方式: db23b3e1-dd8f-4c48-b9df-4afde6e2db70
2025-09-19 15:17:30,702 - __main__ - INFO - 成功处理supplier: 100028
2025-09-19 15:17:30,702 - __main__ - INFO - 进度: 30/215
2025-09-19 15:17:30,702 - __main__ - INFO - 处理supplier: 100029
2025-09-19 15:17:30,703 - __main__ - WARNING - 未找到对应的vendor，跳过: 100029
2025-09-19 15:17:30,703 - __main__ - INFO - 进度: 31/215
2025-09-19 15:17:30,703 - __main__ - INFO - 处理supplier: 100030
2025-09-19 15:17:30,704 - __main__ - INFO - 找到对应vendor: fbfbc84f-f627-45c2-9270-0c782fc90874
2025-09-19 15:17:30,704 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,704 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,704 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,705 - database_manager - INFO - 成功更新vendor: fbfbc84f-f627-45c2-9270-0c782fc90874
2025-09-19 15:17:30,705 - __main__ - INFO - 成功更新vendor: fbfbc84f-f627-45c2-9270-0c782fc90874
2025-09-19 15:17:30,707 - database_manager - INFO - 成功删除vendor联系方式: fbfbc84f-f627-45c2-9270-0c782fc90874
2025-09-19 15:17:30,709 - __main__ - INFO - 成功处理supplier: 100030
2025-09-19 15:17:30,709 - __main__ - INFO - 进度: 32/215
2025-09-19 15:17:30,709 - __main__ - INFO - 处理supplier: 100031
2025-09-19 15:17:30,710 - __main__ - INFO - 找到对应vendor: d817b282-18a9-468c-80a0-6a55dee192ec
2025-09-19 15:17:30,710 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,710 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,711 - database_manager - INFO - 成功更新vendor: d817b282-18a9-468c-80a0-6a55dee192ec
2025-09-19 15:17:30,711 - __main__ - INFO - 成功更新vendor: d817b282-18a9-468c-80a0-6a55dee192ec
2025-09-19 15:17:30,712 - database_manager - INFO - 成功删除vendor联系方式: d817b282-18a9-468c-80a0-6a55dee192ec
2025-09-19 15:17:30,715 - __main__ - INFO - 成功处理supplier: 100031
2025-09-19 15:17:30,715 - __main__ - INFO - 进度: 33/215
2025-09-19 15:17:30,715 - __main__ - INFO - 处理supplier: 100032
2025-09-19 15:17:30,716 - __main__ - INFO - 找到对应vendor: 3e8feedf-3ff5-437e-afb5-6454f5207e86
2025-09-19 15:17:30,716 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,716 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,716 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,717 - database_manager - INFO - 成功更新vendor: 3e8feedf-3ff5-437e-afb5-6454f5207e86
2025-09-19 15:17:30,717 - __main__ - INFO - 成功更新vendor: 3e8feedf-3ff5-437e-afb5-6454f5207e86
2025-09-19 15:17:30,719 - database_manager - INFO - 成功删除vendor联系方式: 3e8feedf-3ff5-437e-afb5-6454f5207e86
2025-09-19 15:17:30,720 - __main__ - INFO - 成功处理supplier: 100032
2025-09-19 15:17:30,720 - __main__ - INFO - 进度: 34/215
2025-09-19 15:17:30,720 - __main__ - INFO - 处理supplier: 100033
2025-09-19 15:17:30,721 - __main__ - WARNING - 未找到对应的vendor，跳过: 100033
2025-09-19 15:17:30,721 - __main__ - INFO - 进度: 35/215
2025-09-19 15:17:30,721 - __main__ - INFO - 处理supplier: 100034
2025-09-19 15:17:30,722 - __main__ - INFO - 找到对应vendor: 2de2123b-02a1-47b5-9dc0-2fa3d2fc371d
2025-09-19 15:17:30,722 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,722 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,722 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,722 - database_manager - INFO - 成功更新vendor: 2de2123b-02a1-47b5-9dc0-2fa3d2fc371d
2025-09-19 15:17:30,723 - __main__ - INFO - 成功更新vendor: 2de2123b-02a1-47b5-9dc0-2fa3d2fc371d
2025-09-19 15:17:30,724 - database_manager - INFO - 成功删除vendor联系方式: 2de2123b-02a1-47b5-9dc0-2fa3d2fc371d
2025-09-19 15:17:30,729 - __main__ - INFO - 成功处理supplier: 100034
2025-09-19 15:17:30,730 - __main__ - INFO - 进度: 36/215
2025-09-19 15:17:30,730 - __main__ - INFO - 处理supplier: 100035
2025-09-19 15:17:30,731 - __main__ - INFO - 找到对应vendor: dbf0440d-a979-4a8e-a066-96b8dd69ecb5
2025-09-19 15:17:30,731 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,731 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,732 - database_manager - INFO - 成功更新vendor: dbf0440d-a979-4a8e-a066-96b8dd69ecb5
2025-09-19 15:17:30,732 - __main__ - INFO - 成功更新vendor: dbf0440d-a979-4a8e-a066-96b8dd69ecb5
2025-09-19 15:17:30,734 - database_manager - INFO - 成功删除vendor联系方式: dbf0440d-a979-4a8e-a066-96b8dd69ecb5
2025-09-19 15:17:30,737 - __main__ - INFO - 成功处理supplier: 100035
2025-09-19 15:17:30,737 - __main__ - INFO - 进度: 37/215
2025-09-19 15:17:30,737 - __main__ - INFO - 处理supplier: 100036
2025-09-19 15:17:30,737 - __main__ - WARNING - 未找到对应的vendor，跳过: 100036
2025-09-19 15:17:30,737 - __main__ - INFO - 进度: 38/215
2025-09-19 15:17:30,738 - __main__ - INFO - 处理supplier: 100037
2025-09-19 15:17:30,738 - __main__ - WARNING - 未找到对应的vendor，跳过: 100037
2025-09-19 15:17:30,738 - __main__ - INFO - 进度: 39/215
2025-09-19 15:17:30,738 - __main__ - INFO - 处理supplier: 100038
2025-09-19 15:17:30,739 - __main__ - WARNING - 未找到对应的vendor，跳过: 100038
2025-09-19 15:17:30,739 - __main__ - INFO - 进度: 40/215
2025-09-19 15:17:30,739 - __main__ - INFO - 处理supplier: 100039
2025-09-19 15:17:30,740 - __main__ - WARNING - 未找到对应的vendor，跳过: 100039
2025-09-19 15:17:30,740 - __main__ - INFO - 进度: 41/215
2025-09-19 15:17:30,740 - __main__ - INFO - 处理supplier: 100040
2025-09-19 15:17:30,740 - __main__ - INFO - 找到对应vendor: 3b9b36e1-4414-434a-bfc0-a000e0ea1c5f
2025-09-19 15:17:30,740 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,740 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,741 - database_manager - INFO - 成功更新vendor: 3b9b36e1-4414-434a-bfc0-a000e0ea1c5f
2025-09-19 15:17:30,741 - __main__ - INFO - 成功更新vendor: 3b9b36e1-4414-434a-bfc0-a000e0ea1c5f
2025-09-19 15:17:30,742 - database_manager - INFO - 成功删除vendor联系方式: 3b9b36e1-4414-434a-bfc0-a000e0ea1c5f
2025-09-19 15:17:30,745 - __main__ - INFO - 成功处理supplier: 100040
2025-09-19 15:17:30,746 - __main__ - INFO - 进度: 42/215
2025-09-19 15:17:30,746 - __main__ - INFO - 处理supplier: 100041
2025-09-19 15:17:30,747 - __main__ - WARNING - 未找到对应的vendor，跳过: 100041
2025-09-19 15:17:30,747 - __main__ - INFO - 进度: 43/215
2025-09-19 15:17:30,747 - __main__ - INFO - 处理supplier: 100042
2025-09-19 15:17:30,748 - __main__ - WARNING - 未找到对应的vendor，跳过: 100042
2025-09-19 15:17:30,748 - __main__ - INFO - 进度: 44/215
2025-09-19 15:17:30,748 - __main__ - INFO - 处理supplier: 100043
2025-09-19 15:17:30,749 - __main__ - WARNING - 未找到对应的vendor，跳过: 100043
2025-09-19 15:17:30,749 - __main__ - INFO - 进度: 45/215
2025-09-19 15:17:30,749 - __main__ - INFO - 处理supplier: 100044
2025-09-19 15:17:30,750 - __main__ - INFO - 找到对应vendor: 6aecfe05-3fb1-4ed0-afb8-3827d0d5452d
2025-09-19 15:17:30,750 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,750 - database_manager - INFO - 成功更新vendor: 6aecfe05-3fb1-4ed0-afb8-3827d0d5452d
2025-09-19 15:17:30,751 - __main__ - INFO - 成功更新vendor: 6aecfe05-3fb1-4ed0-afb8-3827d0d5452d
2025-09-19 15:17:30,752 - database_manager - INFO - 成功删除vendor联系方式: 6aecfe05-3fb1-4ed0-afb8-3827d0d5452d
2025-09-19 15:17:30,753 - __main__ - INFO - 成功处理supplier: 100044
2025-09-19 15:17:30,754 - __main__ - INFO - 进度: 46/215
2025-09-19 15:17:30,754 - __main__ - INFO - 处理supplier: 100045
2025-09-19 15:17:30,754 - __main__ - WARNING - 未找到对应的vendor，跳过: 100045
2025-09-19 15:17:30,754 - __main__ - INFO - 进度: 47/215
2025-09-19 15:17:30,754 - __main__ - INFO - 处理supplier: 100046
2025-09-19 15:17:30,755 - __main__ - WARNING - 未找到对应的vendor，跳过: 100046
2025-09-19 15:17:30,755 - __main__ - INFO - 进度: 48/215
2025-09-19 15:17:30,755 - __main__ - INFO - 处理supplier: 100047
2025-09-19 15:17:30,756 - __main__ - WARNING - 未找到对应的vendor，跳过: 100047
2025-09-19 15:17:30,756 - __main__ - INFO - 进度: 49/215
2025-09-19 15:17:30,756 - __main__ - INFO - 处理supplier: 100048
2025-09-19 15:17:30,756 - __main__ - WARNING - 未找到对应的vendor，跳过: 100048
2025-09-19 15:17:30,756 - __main__ - INFO - 进度: 50/215
2025-09-19 15:17:30,756 - __main__ - INFO - 处理supplier: 100049
2025-09-19 15:17:30,757 - __main__ - WARNING - 未找到对应的vendor，跳过: 100049
2025-09-19 15:17:30,757 - __main__ - INFO - 进度: 51/215
2025-09-19 15:17:30,757 - __main__ - INFO - 处理supplier: 100050
2025-09-19 15:17:30,758 - __main__ - WARNING - 未找到对应的vendor，跳过: 100050
2025-09-19 15:17:30,758 - __main__ - INFO - 进度: 52/215
2025-09-19 15:17:30,758 - __main__ - INFO - 处理supplier: 100051
2025-09-19 15:17:30,758 - __main__ - WARNING - 未找到对应的vendor，跳过: 100051
2025-09-19 15:17:30,758 - __main__ - INFO - 进度: 53/215
2025-09-19 15:17:30,758 - __main__ - INFO - 处理supplier: 100052
2025-09-19 15:17:30,759 - __main__ - INFO - 找到对应vendor: c05a31dc-8f1d-4b50-906c-58604d0cd307
2025-09-19 15:17:30,759 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,760 - database_manager - INFO - 成功更新vendor: c05a31dc-8f1d-4b50-906c-58604d0cd307
2025-09-19 15:17:30,760 - __main__ - INFO - 成功更新vendor: c05a31dc-8f1d-4b50-906c-58604d0cd307
2025-09-19 15:17:30,761 - database_manager - INFO - 成功删除vendor联系方式: c05a31dc-8f1d-4b50-906c-58604d0cd307
2025-09-19 15:17:30,764 - __main__ - INFO - 成功处理supplier: 100052
2025-09-19 15:17:30,764 - __main__ - INFO - 进度: 54/215
2025-09-19 15:17:30,764 - __main__ - INFO - 处理supplier: 100053
2025-09-19 15:17:30,765 - __main__ - WARNING - 未找到对应的vendor，跳过: 100053
2025-09-19 15:17:30,765 - __main__ - INFO - 进度: 55/215
2025-09-19 15:17:30,765 - __main__ - INFO - 处理supplier: 100054
2025-09-19 15:17:30,766 - __main__ - WARNING - 未找到对应的vendor，跳过: 100054
2025-09-19 15:17:30,766 - __main__ - INFO - 进度: 56/215
2025-09-19 15:17:30,766 - __main__ - INFO - 处理supplier: 100055
2025-09-19 15:17:30,767 - __main__ - INFO - 找到对应vendor: f133da09-8c0b-46db-9168-78135a5cc737
2025-09-19 15:17:30,767 - database_manager - INFO - 成功更新vendor: f133da09-8c0b-46db-9168-78135a5cc737
2025-09-19 15:17:30,768 - __main__ - INFO - 成功更新vendor: f133da09-8c0b-46db-9168-78135a5cc737
2025-09-19 15:17:30,769 - database_manager - INFO - 成功删除vendor联系方式: f133da09-8c0b-46db-9168-78135a5cc737
2025-09-19 15:17:30,771 - __main__ - INFO - 成功处理supplier: 100055
2025-09-19 15:17:30,771 - __main__ - INFO - 进度: 57/215
2025-09-19 15:17:30,771 - __main__ - INFO - 处理supplier: 100056
2025-09-19 15:17:30,772 - __main__ - WARNING - 未找到对应的vendor，跳过: 100056
2025-09-19 15:17:30,772 - __main__ - INFO - 进度: 58/215
2025-09-19 15:17:30,772 - __main__ - INFO - 处理supplier: 100057
2025-09-19 15:17:30,773 - __main__ - WARNING - 未找到对应的vendor，跳过: 100057
2025-09-19 15:17:30,773 - __main__ - INFO - 进度: 59/215
2025-09-19 15:17:30,773 - __main__ - INFO - 处理supplier: 100058
2025-09-19 15:17:30,773 - __main__ - WARNING - 未找到对应的vendor，跳过: 100058
2025-09-19 15:17:30,773 - __main__ - INFO - 进度: 60/215
2025-09-19 15:17:30,773 - __main__ - INFO - 处理supplier: 100059
2025-09-19 15:17:30,774 - __main__ - WARNING - 未找到对应的vendor，跳过: 100059
2025-09-19 15:17:30,774 - __main__ - INFO - 进度: 61/215
2025-09-19 15:17:30,774 - __main__ - INFO - 处理supplier: 100060
2025-09-19 15:17:30,774 - __main__ - INFO - 找到对应vendor: def22cec-77a4-4a4a-9411-810f58b23c3a
2025-09-19 15:17:30,775 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,775 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,775 - database_manager - INFO - 成功更新vendor: def22cec-77a4-4a4a-9411-810f58b23c3a
2025-09-19 15:17:30,775 - __main__ - INFO - 成功更新vendor: def22cec-77a4-4a4a-9411-810f58b23c3a
2025-09-19 15:17:30,776 - database_manager - INFO - 成功删除vendor联系方式: def22cec-77a4-4a4a-9411-810f58b23c3a
2025-09-19 15:17:30,787 - __main__ - INFO - 成功处理supplier: 100060
2025-09-19 15:17:30,787 - __main__ - INFO - 进度: 62/215
2025-09-19 15:17:30,787 - __main__ - INFO - 处理supplier: 100061
2025-09-19 15:17:30,789 - __main__ - WARNING - 未找到对应的vendor，跳过: 100061
2025-09-19 15:17:30,789 - __main__ - INFO - 进度: 63/215
2025-09-19 15:17:30,789 - __main__ - INFO - 处理supplier: 100062
2025-09-19 15:17:30,790 - __main__ - INFO - 找到对应vendor: 8cb7ec6d-a047-45a6-ba78-b3c99f40d67f
2025-09-19 15:17:30,791 - database_manager - INFO - 成功更新vendor: 8cb7ec6d-a047-45a6-ba78-b3c99f40d67f
2025-09-19 15:17:30,791 - __main__ - INFO - 成功更新vendor: 8cb7ec6d-a047-45a6-ba78-b3c99f40d67f
2025-09-19 15:17:30,793 - database_manager - INFO - 成功删除vendor联系方式: 8cb7ec6d-a047-45a6-ba78-b3c99f40d67f
2025-09-19 15:17:30,799 - __main__ - INFO - 成功处理supplier: 100062
2025-09-19 15:17:30,799 - __main__ - INFO - 进度: 64/215
2025-09-19 15:17:30,799 - __main__ - INFO - 处理supplier: 100063
2025-09-19 15:17:30,801 - __main__ - WARNING - 未找到对应的vendor，跳过: 100063
2025-09-19 15:17:30,801 - __main__ - INFO - 进度: 65/215
2025-09-19 15:17:30,801 - __main__ - INFO - 处理supplier: 100064
2025-09-19 15:17:30,802 - __main__ - INFO - 找到对应vendor: 8472df1c-021d-4bd0-8499-70ae338c53d1
2025-09-19 15:17:30,802 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,802 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,802 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,802 - data_mapper - WARNING - 邮箱地址无效，跳过
2025-09-19 15:17:30,803 - database_manager - INFO - 成功更新vendor: 8472df1c-021d-4bd0-8499-70ae338c53d1
2025-09-19 15:17:30,803 - __main__ - INFO - 成功更新vendor: 8472df1c-021d-4bd0-8499-70ae338c53d1
2025-09-19 15:17:30,805 - database_manager - INFO - 成功删除vendor联系方式: 8472df1c-021d-4bd0-8499-70ae338c53d1
2025-09-19 15:17:30,810 - __main__ - INFO - 成功处理supplier: 100064
2025-09-19 15:17:30,810 - __main__ - INFO - 进度: 66/215
2025-09-19 15:17:30,810 - __main__ - INFO - 处理supplier: 100065
2025-09-19 15:17:30,811 - __main__ - INFO - 找到对应vendor: c758516a-ef19-421b-8d19-c55dcf4bce5f
2025-09-19 15:17:30,812 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,812 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,812 - database_manager - INFO - 成功更新vendor: c758516a-ef19-421b-8d19-c55dcf4bce5f
2025-09-19 15:17:30,814 - __main__ - INFO - 成功更新vendor: c758516a-ef19-421b-8d19-c55dcf4bce5f
2025-09-19 15:17:30,817 - database_manager - INFO - 成功删除vendor联系方式: c758516a-ef19-421b-8d19-c55dcf4bce5f
2025-09-19 15:17:30,820 - __main__ - INFO - 成功处理supplier: 100065
2025-09-19 15:17:30,820 - __main__ - INFO - 进度: 67/215
2025-09-19 15:17:30,820 - __main__ - INFO - 处理supplier: 100066
2025-09-19 15:17:30,821 - __main__ - INFO - 找到对应vendor: 89e39f33-7509-4743-a4f8-ec4ee761f449
2025-09-19 15:17:30,821 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,821 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,822 - database_manager - INFO - 成功更新vendor: 89e39f33-7509-4743-a4f8-ec4ee761f449
2025-09-19 15:17:30,823 - __main__ - INFO - 成功更新vendor: 89e39f33-7509-4743-a4f8-ec4ee761f449
2025-09-19 15:17:30,824 - database_manager - INFO - 成功删除vendor联系方式: 89e39f33-7509-4743-a4f8-ec4ee761f449
2025-09-19 15:17:30,827 - __main__ - INFO - 成功处理supplier: 100066
2025-09-19 15:17:30,827 - __main__ - INFO - 进度: 68/215
2025-09-19 15:17:30,827 - __main__ - INFO - 处理supplier: 100067
2025-09-19 15:17:30,827 - __main__ - INFO - 找到对应vendor: 61fb4a48-b14a-455a-b7ca-0761ddc1542e
2025-09-19 15:17:30,828 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,828 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,828 - database_manager - INFO - 成功更新vendor: 61fb4a48-b14a-455a-b7ca-0761ddc1542e
2025-09-19 15:17:30,829 - __main__ - INFO - 成功更新vendor: 61fb4a48-b14a-455a-b7ca-0761ddc1542e
2025-09-19 15:17:30,830 - database_manager - INFO - 成功删除vendor联系方式: 61fb4a48-b14a-455a-b7ca-0761ddc1542e
2025-09-19 15:17:30,835 - __main__ - INFO - 成功处理supplier: 100067
2025-09-19 15:17:30,835 - __main__ - INFO - 进度: 69/215
2025-09-19 15:17:30,835 - __main__ - INFO - 处理supplier: 100068
2025-09-19 15:17:30,836 - __main__ - INFO - 找到对应vendor: af7e1257-0990-43d4-b61c-52405af59c19
2025-09-19 15:17:30,836 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,836 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,836 - database_manager - INFO - 成功更新vendor: af7e1257-0990-43d4-b61c-52405af59c19
2025-09-19 15:17:30,837 - __main__ - INFO - 成功更新vendor: af7e1257-0990-43d4-b61c-52405af59c19
2025-09-19 15:17:30,838 - database_manager - INFO - 成功删除vendor联系方式: af7e1257-0990-43d4-b61c-52405af59c19
2025-09-19 15:17:30,840 - __main__ - INFO - 成功处理supplier: 100068
2025-09-19 15:17:30,840 - __main__ - INFO - 进度: 70/215
2025-09-19 15:17:30,840 - __main__ - INFO - 处理supplier: 100069
2025-09-19 15:17:30,841 - __main__ - INFO - 找到对应vendor: 23acd2ff-09b4-43b2-b345-c1ac11674123
2025-09-19 15:17:30,841 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,841 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,841 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,841 - database_manager - INFO - 成功更新vendor: 23acd2ff-09b4-43b2-b345-c1ac11674123
2025-09-19 15:17:30,842 - __main__ - INFO - 成功更新vendor: 23acd2ff-09b4-43b2-b345-c1ac11674123
2025-09-19 15:17:30,843 - database_manager - INFO - 成功删除vendor联系方式: 23acd2ff-09b4-43b2-b345-c1ac11674123
2025-09-19 15:17:30,847 - __main__ - INFO - 成功处理supplier: 100069
2025-09-19 15:17:30,847 - __main__ - INFO - 进度: 71/215
2025-09-19 15:17:30,847 - __main__ - INFO - 处理supplier: 100070
2025-09-19 15:17:30,849 - __main__ - WARNING - 未找到对应的vendor，跳过: 100070
2025-09-19 15:17:30,849 - __main__ - INFO - 进度: 72/215
2025-09-19 15:17:30,849 - __main__ - INFO - 处理supplier: 100071
2025-09-19 15:17:30,850 - __main__ - WARNING - 未找到对应的vendor，跳过: 100071
2025-09-19 15:17:30,850 - __main__ - INFO - 进度: 73/215
2025-09-19 15:17:30,850 - __main__ - INFO - 处理supplier: 100072
2025-09-19 15:17:30,851 - __main__ - WARNING - 未找到对应的vendor，跳过: 100072
2025-09-19 15:17:30,851 - __main__ - INFO - 进度: 74/215
2025-09-19 15:17:30,851 - __main__ - INFO - 处理supplier: 100073
2025-09-19 15:17:30,851 - __main__ - INFO - 找到对应vendor: 39ec865b-ba20-49fa-baa6-a41589ee5d8a
2025-09-19 15:17:30,851 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,851 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,851 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,852 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,852 - database_manager - INFO - 成功更新vendor: 39ec865b-ba20-49fa-baa6-a41589ee5d8a
2025-09-19 15:17:30,853 - __main__ - INFO - 成功更新vendor: 39ec865b-ba20-49fa-baa6-a41589ee5d8a
2025-09-19 15:17:30,854 - database_manager - INFO - 成功删除vendor联系方式: 39ec865b-ba20-49fa-baa6-a41589ee5d8a
2025-09-19 15:17:30,857 - __main__ - INFO - 成功处理supplier: 100073
2025-09-19 15:17:30,858 - __main__ - INFO - 进度: 75/215
2025-09-19 15:17:30,858 - __main__ - INFO - 处理supplier: 100074
2025-09-19 15:17:30,858 - __main__ - INFO - 找到对应vendor: d22d7073-1ea0-4db6-b72d-d37cfddfccc8
2025-09-19 15:17:30,858 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,859 - database_manager - INFO - 成功更新vendor: d22d7073-1ea0-4db6-b72d-d37cfddfccc8
2025-09-19 15:17:30,860 - __main__ - INFO - 成功更新vendor: d22d7073-1ea0-4db6-b72d-d37cfddfccc8
2025-09-19 15:17:30,861 - database_manager - INFO - 成功删除vendor联系方式: d22d7073-1ea0-4db6-b72d-d37cfddfccc8
2025-09-19 15:17:30,863 - __main__ - INFO - 成功处理supplier: 100074
2025-09-19 15:17:30,863 - __main__ - INFO - 进度: 76/215
2025-09-19 15:17:30,863 - __main__ - INFO - 处理supplier: 100075
2025-09-19 15:17:30,864 - __main__ - INFO - 找到对应vendor: e07a8ae6-eda7-4926-82f8-fb90493cf5ae
2025-09-19 15:17:30,865 - database_manager - INFO - 成功更新vendor: e07a8ae6-eda7-4926-82f8-fb90493cf5ae
2025-09-19 15:17:30,866 - __main__ - INFO - 成功更新vendor: e07a8ae6-eda7-4926-82f8-fb90493cf5ae
2025-09-19 15:17:30,868 - database_manager - INFO - 成功删除vendor联系方式: e07a8ae6-eda7-4926-82f8-fb90493cf5ae
2025-09-19 15:17:30,869 - __main__ - INFO - 成功处理supplier: 100075
2025-09-19 15:17:30,869 - __main__ - INFO - 进度: 77/215
2025-09-19 15:17:30,869 - __main__ - INFO - 处理supplier: 100076
2025-09-19 15:17:30,869 - __main__ - WARNING - 未找到对应的vendor，跳过: 100076
2025-09-19 15:17:30,869 - __main__ - INFO - 进度: 78/215
2025-09-19 15:17:30,870 - __main__ - INFO - 处理supplier: 100077
2025-09-19 15:17:30,870 - __main__ - WARNING - 未找到对应的vendor，跳过: 100077
2025-09-19 15:17:30,870 - __main__ - INFO - 进度: 79/215
2025-09-19 15:17:30,870 - __main__ - INFO - 处理supplier: 100078
2025-09-19 15:17:30,871 - __main__ - WARNING - 未找到对应的vendor，跳过: 100078
2025-09-19 15:17:30,871 - __main__ - INFO - 进度: 80/215
2025-09-19 15:17:30,871 - __main__ - INFO - 处理supplier: 100079
2025-09-19 15:17:30,872 - __main__ - INFO - 找到对应vendor: aff88d19-cfb0-41db-8804-48dc9dde98ce
2025-09-19 15:17:30,872 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,872 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,873 - database_manager - INFO - 成功更新vendor: aff88d19-cfb0-41db-8804-48dc9dde98ce
2025-09-19 15:17:30,874 - __main__ - INFO - 成功更新vendor: aff88d19-cfb0-41db-8804-48dc9dde98ce
2025-09-19 15:17:30,875 - database_manager - INFO - 成功删除vendor联系方式: aff88d19-cfb0-41db-8804-48dc9dde98ce
2025-09-19 15:17:30,878 - __main__ - INFO - 成功处理supplier: 100079
2025-09-19 15:17:30,878 - __main__ - INFO - 进度: 81/215
2025-09-19 15:17:30,878 - __main__ - INFO - 处理supplier: 100080
2025-09-19 15:17:30,880 - __main__ - INFO - 找到对应vendor: dde584c3-5356-4ba7-bd37-b3ddc92b8b7d
2025-09-19 15:17:30,880 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,880 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,881 - database_manager - INFO - 成功更新vendor: dde584c3-5356-4ba7-bd37-b3ddc92b8b7d
2025-09-19 15:17:30,882 - __main__ - INFO - 成功更新vendor: dde584c3-5356-4ba7-bd37-b3ddc92b8b7d
2025-09-19 15:17:30,883 - database_manager - INFO - 成功删除vendor联系方式: dde584c3-5356-4ba7-bd37-b3ddc92b8b7d
2025-09-19 15:17:30,885 - __main__ - INFO - 成功处理supplier: 100080
2025-09-19 15:17:30,885 - __main__ - INFO - 进度: 82/215
2025-09-19 15:17:30,885 - __main__ - INFO - 处理supplier: 100081
2025-09-19 15:17:30,886 - __main__ - WARNING - 未找到对应的vendor，跳过: 100081
2025-09-19 15:17:30,886 - __main__ - INFO - 进度: 83/215
2025-09-19 15:17:30,886 - __main__ - INFO - 处理supplier: 100082
2025-09-19 15:17:30,887 - __main__ - INFO - 找到对应vendor: e2b00391-ae40-44dd-b563-2bec55d8c8be
2025-09-19 15:17:30,887 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,887 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,887 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,887 - database_manager - INFO - 成功更新vendor: e2b00391-ae40-44dd-b563-2bec55d8c8be
2025-09-19 15:17:30,888 - __main__ - INFO - 成功更新vendor: e2b00391-ae40-44dd-b563-2bec55d8c8be
2025-09-19 15:17:30,889 - database_manager - INFO - 成功删除vendor联系方式: e2b00391-ae40-44dd-b563-2bec55d8c8be
2025-09-19 15:17:30,891 - __main__ - INFO - 成功处理supplier: 100082
2025-09-19 15:17:30,891 - __main__ - INFO - 进度: 84/215
2025-09-19 15:17:30,891 - __main__ - INFO - 处理supplier: 100083
2025-09-19 15:17:30,892 - __main__ - WARNING - 未找到对应的vendor，跳过: 100083
2025-09-19 15:17:30,892 - __main__ - INFO - 进度: 85/215
2025-09-19 15:17:30,892 - __main__ - INFO - 处理supplier: 100084
2025-09-19 15:17:30,893 - __main__ - INFO - 找到对应vendor: e4ebc7b0-c2b6-40ff-b1ee-35f0e6cb63f0
2025-09-19 15:17:30,893 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,893 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,893 - database_manager - INFO - 成功更新vendor: e4ebc7b0-c2b6-40ff-b1ee-35f0e6cb63f0
2025-09-19 15:17:30,893 - __main__ - INFO - 成功更新vendor: e4ebc7b0-c2b6-40ff-b1ee-35f0e6cb63f0
2025-09-19 15:17:30,894 - database_manager - INFO - 成功删除vendor联系方式: e4ebc7b0-c2b6-40ff-b1ee-35f0e6cb63f0
2025-09-19 15:17:30,897 - __main__ - INFO - 成功处理supplier: 100084
2025-09-19 15:17:30,897 - __main__ - INFO - 进度: 86/215
2025-09-19 15:17:30,897 - __main__ - INFO - 处理supplier: 100085
2025-09-19 15:17:30,898 - __main__ - INFO - 找到对应vendor: 697f79d3-2092-4e1c-a3ff-b2fe07831fd8
2025-09-19 15:17:30,898 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,898 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,898 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,898 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,898 - database_manager - INFO - 成功更新vendor: 697f79d3-2092-4e1c-a3ff-b2fe07831fd8
2025-09-19 15:17:30,898 - __main__ - INFO - 成功更新vendor: 697f79d3-2092-4e1c-a3ff-b2fe07831fd8
2025-09-19 15:17:30,899 - database_manager - INFO - 成功删除vendor联系方式: 697f79d3-2092-4e1c-a3ff-b2fe07831fd8
2025-09-19 15:17:30,901 - __main__ - INFO - 成功处理supplier: 100085
2025-09-19 15:17:30,901 - __main__ - INFO - 进度: 87/215
2025-09-19 15:17:30,901 - __main__ - INFO - 处理supplier: 100086
2025-09-19 15:17:30,902 - __main__ - INFO - 找到对应vendor: 2ee01f74-7100-4b20-a36e-c72bdeb3031f
2025-09-19 15:17:30,902 - database_manager - INFO - 成功更新vendor: 2ee01f74-7100-4b20-a36e-c72bdeb3031f
2025-09-19 15:17:30,902 - __main__ - INFO - 成功更新vendor: 2ee01f74-7100-4b20-a36e-c72bdeb3031f
2025-09-19 15:17:30,903 - database_manager - INFO - 成功删除vendor联系方式: 2ee01f74-7100-4b20-a36e-c72bdeb3031f
2025-09-19 15:17:30,904 - __main__ - INFO - 成功处理supplier: 100086
2025-09-19 15:17:30,904 - __main__ - INFO - 进度: 88/215
2025-09-19 15:17:30,904 - __main__ - INFO - 处理supplier: 100087
2025-09-19 15:17:30,905 - __main__ - INFO - 找到对应vendor: fe8fcd49-ea3d-43a8-b8fb-3b3429986603
2025-09-19 15:17:30,905 - database_manager - INFO - 成功更新vendor: fe8fcd49-ea3d-43a8-b8fb-3b3429986603
2025-09-19 15:17:30,905 - __main__ - INFO - 成功更新vendor: fe8fcd49-ea3d-43a8-b8fb-3b3429986603
2025-09-19 15:17:30,906 - database_manager - INFO - 成功删除vendor联系方式: fe8fcd49-ea3d-43a8-b8fb-3b3429986603
2025-09-19 15:17:30,907 - __main__ - INFO - 成功处理supplier: 100087
2025-09-19 15:17:30,907 - __main__ - INFO - 进度: 89/215
2025-09-19 15:17:30,907 - __main__ - INFO - 处理supplier: 100088
2025-09-19 15:17:30,907 - __main__ - WARNING - 未找到对应的vendor，跳过: 100088
2025-09-19 15:17:30,907 - __main__ - INFO - 进度: 90/215
2025-09-19 15:17:30,907 - __main__ - INFO - 处理supplier: 100089
2025-09-19 15:17:30,908 - __main__ - INFO - 找到对应vendor: 71451235-4c7d-4001-a0d2-d1d03244cb4d
2025-09-19 15:17:30,908 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,908 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,908 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,908 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,908 - database_manager - INFO - 成功更新vendor: 71451235-4c7d-4001-a0d2-d1d03244cb4d
2025-09-19 15:17:30,909 - __main__ - INFO - 成功更新vendor: 71451235-4c7d-4001-a0d2-d1d03244cb4d
2025-09-19 15:17:30,910 - database_manager - INFO - 成功删除vendor联系方式: 71451235-4c7d-4001-a0d2-d1d03244cb4d
2025-09-19 15:17:30,911 - __main__ - INFO - 成功处理supplier: 100089
2025-09-19 15:17:30,911 - __main__ - INFO - 进度: 91/215
2025-09-19 15:17:30,911 - __main__ - INFO - 处理supplier: 100090
2025-09-19 15:17:30,911 - __main__ - WARNING - 未找到对应的vendor，跳过: 100090
2025-09-19 15:17:30,911 - __main__ - INFO - 进度: 92/215
2025-09-19 15:17:30,911 - __main__ - INFO - 处理supplier: 100091
2025-09-19 15:17:30,912 - __main__ - INFO - 找到对应vendor: d0f1b85a-a9b8-4257-8a2d-980b0038994e
2025-09-19 15:17:30,912 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,912 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,912 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,913 - database_manager - INFO - 成功更新vendor: d0f1b85a-a9b8-4257-8a2d-980b0038994e
2025-09-19 15:17:30,913 - __main__ - INFO - 成功更新vendor: d0f1b85a-a9b8-4257-8a2d-980b0038994e
2025-09-19 15:17:30,914 - database_manager - INFO - 成功删除vendor联系方式: d0f1b85a-a9b8-4257-8a2d-980b0038994e
2025-09-19 15:17:30,917 - __main__ - INFO - 成功处理supplier: 100091
2025-09-19 15:17:30,917 - __main__ - INFO - 进度: 93/215
2025-09-19 15:17:30,917 - __main__ - INFO - 处理supplier: 100092
2025-09-19 15:17:30,917 - __main__ - INFO - 找到对应vendor: be0a63c3-03dc-4f3c-8ab7-8b20ae040d5b
2025-09-19 15:17:30,918 - database_manager - INFO - 成功更新vendor: be0a63c3-03dc-4f3c-8ab7-8b20ae040d5b
2025-09-19 15:17:30,918 - __main__ - INFO - 成功更新vendor: be0a63c3-03dc-4f3c-8ab7-8b20ae040d5b
2025-09-19 15:17:30,920 - database_manager - INFO - 成功删除vendor联系方式: be0a63c3-03dc-4f3c-8ab7-8b20ae040d5b
2025-09-19 15:17:30,921 - __main__ - INFO - 成功处理supplier: 100092
2025-09-19 15:17:30,921 - __main__ - INFO - 进度: 94/215
2025-09-19 15:17:30,921 - __main__ - INFO - 处理supplier: 100093
2025-09-19 15:17:30,922 - __main__ - INFO - 找到对应vendor: 187d6ae5-9b38-4d82-9bd0-c3d3c828ba56
2025-09-19 15:17:30,923 - database_manager - INFO - 成功更新vendor: 187d6ae5-9b38-4d82-9bd0-c3d3c828ba56
2025-09-19 15:17:30,923 - __main__ - INFO - 成功更新vendor: 187d6ae5-9b38-4d82-9bd0-c3d3c828ba56
2025-09-19 15:17:30,924 - database_manager - INFO - 成功删除vendor联系方式: 187d6ae5-9b38-4d82-9bd0-c3d3c828ba56
2025-09-19 15:17:30,925 - __main__ - INFO - 成功处理supplier: 100093
2025-09-19 15:17:30,925 - __main__ - INFO - 进度: 95/215
2025-09-19 15:17:30,925 - __main__ - INFO - 处理supplier: 100094
2025-09-19 15:17:30,925 - __main__ - INFO - 找到对应vendor: 567f5386-8c1a-48af-95b9-fd89956ffe82
2025-09-19 15:17:30,926 - database_manager - INFO - 成功更新vendor: 567f5386-8c1a-48af-95b9-fd89956ffe82
2025-09-19 15:17:30,926 - __main__ - INFO - 成功更新vendor: 567f5386-8c1a-48af-95b9-fd89956ffe82
2025-09-19 15:17:30,927 - database_manager - INFO - 成功删除vendor联系方式: 567f5386-8c1a-48af-95b9-fd89956ffe82
2025-09-19 15:17:30,928 - __main__ - INFO - 成功处理supplier: 100094
2025-09-19 15:17:30,928 - __main__ - INFO - 进度: 96/215
2025-09-19 15:17:30,928 - __main__ - INFO - 处理supplier: 100095
2025-09-19 15:17:30,928 - __main__ - INFO - 找到对应vendor: 54e5e1c3-029e-4b4d-af01-57acee317a60
2025-09-19 15:17:30,929 - database_manager - INFO - 成功更新vendor: 54e5e1c3-029e-4b4d-af01-57acee317a60
2025-09-19 15:17:30,929 - __main__ - INFO - 成功更新vendor: 54e5e1c3-029e-4b4d-af01-57acee317a60
2025-09-19 15:17:30,930 - database_manager - INFO - 成功删除vendor联系方式: 54e5e1c3-029e-4b4d-af01-57acee317a60
2025-09-19 15:17:30,932 - __main__ - INFO - 成功处理supplier: 100095
2025-09-19 15:17:30,932 - __main__ - INFO - 进度: 97/215
2025-09-19 15:17:30,932 - __main__ - INFO - 处理supplier: 100096
2025-09-19 15:17:30,932 - __main__ - WARNING - 未找到对应的vendor，跳过: 100096
2025-09-19 15:17:30,932 - __main__ - INFO - 进度: 98/215
2025-09-19 15:17:30,932 - __main__ - INFO - 处理supplier: 100097
2025-09-19 15:17:30,933 - __main__ - INFO - 找到对应vendor: 83c78006-3fd0-4e72-8793-d9a506b6e5f6
2025-09-19 15:17:30,933 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,933 - database_manager - INFO - 成功更新vendor: 83c78006-3fd0-4e72-8793-d9a506b6e5f6
2025-09-19 15:17:30,934 - __main__ - INFO - 成功更新vendor: 83c78006-3fd0-4e72-8793-d9a506b6e5f6
2025-09-19 15:17:30,935 - database_manager - INFO - 成功删除vendor联系方式: 83c78006-3fd0-4e72-8793-d9a506b6e5f6
2025-09-19 15:17:30,935 - __main__ - INFO - 成功处理supplier: 100097
2025-09-19 15:17:30,935 - __main__ - INFO - 进度: 99/215
2025-09-19 15:17:30,935 - __main__ - INFO - 处理supplier: 100098
2025-09-19 15:17:30,936 - __main__ - INFO - 找到对应vendor: d6a7d20a-3104-445f-88ad-cdfc1553ddc8
2025-09-19 15:17:30,937 - database_manager - INFO - 成功更新vendor: d6a7d20a-3104-445f-88ad-cdfc1553ddc8
2025-09-19 15:17:30,937 - __main__ - INFO - 成功更新vendor: d6a7d20a-3104-445f-88ad-cdfc1553ddc8
2025-09-19 15:17:30,938 - database_manager - INFO - 成功删除vendor联系方式: d6a7d20a-3104-445f-88ad-cdfc1553ddc8
2025-09-19 15:17:30,939 - __main__ - INFO - 成功处理supplier: 100098
2025-09-19 15:17:30,939 - __main__ - INFO - 进度: 100/215
2025-09-19 15:17:30,939 - __main__ - INFO - 处理supplier: 100099
2025-09-19 15:17:30,940 - __main__ - WARNING - 未找到对应的vendor，跳过: 100099
2025-09-19 15:17:30,940 - __main__ - INFO - 进度: 101/215
2025-09-19 15:17:30,940 - __main__ - INFO - 处理supplier: 100100
2025-09-19 15:17:30,940 - __main__ - INFO - 找到对应vendor: 09028e57-e742-499b-b959-bcfcaa0198bb
2025-09-19 15:17:30,940 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,940 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,941 - database_manager - INFO - 成功更新vendor: 09028e57-e742-499b-b959-bcfcaa0198bb
2025-09-19 15:17:30,941 - __main__ - INFO - 成功更新vendor: 09028e57-e742-499b-b959-bcfcaa0198bb
2025-09-19 15:17:30,942 - database_manager - INFO - 成功删除vendor联系方式: 09028e57-e742-499b-b959-bcfcaa0198bb
2025-09-19 15:17:30,943 - __main__ - INFO - 成功处理supplier: 100100
2025-09-19 15:17:30,943 - __main__ - INFO - 进度: 102/215
2025-09-19 15:17:30,943 - __main__ - INFO - 处理supplier: 100101
2025-09-19 15:17:30,944 - __main__ - INFO - 找到对应vendor: e8e274f6-3a38-4de1-ab2c-c703cc91c630
2025-09-19 15:17:30,944 - database_manager - INFO - 成功更新vendor: e8e274f6-3a38-4de1-ab2c-c703cc91c630
2025-09-19 15:17:30,945 - __main__ - INFO - 成功更新vendor: e8e274f6-3a38-4de1-ab2c-c703cc91c630
2025-09-19 15:17:30,945 - database_manager - INFO - 成功删除vendor联系方式: e8e274f6-3a38-4de1-ab2c-c703cc91c630
2025-09-19 15:17:30,946 - __main__ - INFO - 成功处理supplier: 100101
2025-09-19 15:17:30,946 - __main__ - INFO - 进度: 103/215
2025-09-19 15:17:30,946 - __main__ - INFO - 处理supplier: 100102
2025-09-19 15:17:30,947 - __main__ - WARNING - 未找到对应的vendor，跳过: 100102
2025-09-19 15:17:30,947 - __main__ - INFO - 进度: 104/215
2025-09-19 15:17:30,947 - __main__ - INFO - 处理supplier: 100103
2025-09-19 15:17:30,947 - __main__ - WARNING - 未找到对应的vendor，跳过: 100103
2025-09-19 15:17:30,947 - __main__ - INFO - 进度: 105/215
2025-09-19 15:17:30,947 - __main__ - INFO - 处理supplier: 100104
2025-09-19 15:17:30,948 - __main__ - WARNING - 未找到对应的vendor，跳过: 100104
2025-09-19 15:17:30,948 - __main__ - INFO - 进度: 106/215
2025-09-19 15:17:30,948 - __main__ - INFO - 处理supplier: 100105
2025-09-19 15:17:30,948 - __main__ - INFO - 找到对应vendor: a9b1a81f-25e4-4695-adbc-18cb98d7722c
2025-09-19 15:17:30,948 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,948 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,949 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,949 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,949 - database_manager - INFO - 成功更新vendor: a9b1a81f-25e4-4695-adbc-18cb98d7722c
2025-09-19 15:17:30,949 - __main__ - INFO - 成功更新vendor: a9b1a81f-25e4-4695-adbc-18cb98d7722c
2025-09-19 15:17:30,950 - database_manager - INFO - 成功删除vendor联系方式: a9b1a81f-25e4-4695-adbc-18cb98d7722c
2025-09-19 15:17:30,954 - __main__ - INFO - 成功处理supplier: 100105
2025-09-19 15:17:30,954 - __main__ - INFO - 进度: 107/215
2025-09-19 15:17:30,954 - __main__ - INFO - 处理supplier: 100106
2025-09-19 15:17:30,955 - __main__ - INFO - 找到对应vendor: fcee8cc0-4924-40ca-87ba-acb09127e13e
2025-09-19 15:17:30,955 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,955 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,955 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,955 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,956 - database_manager - INFO - 成功更新vendor: fcee8cc0-4924-40ca-87ba-acb09127e13e
2025-09-19 15:17:30,956 - __main__ - INFO - 成功更新vendor: fcee8cc0-4924-40ca-87ba-acb09127e13e
2025-09-19 15:17:30,957 - database_manager - INFO - 成功删除vendor联系方式: fcee8cc0-4924-40ca-87ba-acb09127e13e
2025-09-19 15:17:30,958 - __main__ - INFO - 成功处理supplier: 100106
2025-09-19 15:17:30,958 - __main__ - INFO - 进度: 108/215
2025-09-19 15:17:30,959 - __main__ - INFO - 处理supplier: 100107
2025-09-19 15:17:30,959 - __main__ - INFO - 找到对应vendor: 00b6e480-09ba-4088-a509-1250388ce746
2025-09-19 15:17:30,959 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,959 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,959 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,960 - database_manager - INFO - 成功更新vendor: 00b6e480-09ba-4088-a509-1250388ce746
2025-09-19 15:17:30,960 - __main__ - INFO - 成功更新vendor: 00b6e480-09ba-4088-a509-1250388ce746
2025-09-19 15:17:30,961 - database_manager - INFO - 成功删除vendor联系方式: 00b6e480-09ba-4088-a509-1250388ce746
2025-09-19 15:17:30,963 - __main__ - INFO - 成功处理supplier: 100107
2025-09-19 15:17:30,963 - __main__ - INFO - 进度: 109/215
2025-09-19 15:17:30,964 - __main__ - INFO - 处理supplier: 100108
2025-09-19 15:17:30,964 - __main__ - INFO - 找到对应vendor: b54d12ad-7820-4492-ae05-9d1f2df4e92d
2025-09-19 15:17:30,964 - database_manager - INFO - 成功更新vendor: b54d12ad-7820-4492-ae05-9d1f2df4e92d
2025-09-19 15:17:30,965 - __main__ - INFO - 成功更新vendor: b54d12ad-7820-4492-ae05-9d1f2df4e92d
2025-09-19 15:17:30,966 - database_manager - INFO - 成功删除vendor联系方式: b54d12ad-7820-4492-ae05-9d1f2df4e92d
2025-09-19 15:17:30,966 - __main__ - INFO - 成功处理supplier: 100108
2025-09-19 15:17:30,966 - __main__ - INFO - 进度: 110/215
2025-09-19 15:17:30,966 - __main__ - INFO - 处理supplier: 100109
2025-09-19 15:17:30,967 - __main__ - INFO - 找到对应vendor: e4dc22fc-26ec-41b5-83f5-b1c010827edb
2025-09-19 15:17:30,967 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,967 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,967 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,968 - database_manager - INFO - 成功更新vendor: e4dc22fc-26ec-41b5-83f5-b1c010827edb
2025-09-19 15:17:30,968 - __main__ - INFO - 成功更新vendor: e4dc22fc-26ec-41b5-83f5-b1c010827edb
2025-09-19 15:17:30,969 - database_manager - INFO - 成功删除vendor联系方式: e4dc22fc-26ec-41b5-83f5-b1c010827edb
2025-09-19 15:17:30,981 - __main__ - INFO - 成功处理supplier: 100109
2025-09-19 15:17:30,981 - __main__ - INFO - 进度: 111/215
2025-09-19 15:17:30,981 - __main__ - INFO - 处理supplier: 100110
2025-09-19 15:17:30,983 - __main__ - INFO - 找到对应vendor: 0f396647-1cc4-4a99-892b-b42ee1d82423
2025-09-19 15:17:30,984 - database_manager - INFO - 成功更新vendor: 0f396647-1cc4-4a99-892b-b42ee1d82423
2025-09-19 15:17:30,985 - __main__ - INFO - 成功更新vendor: 0f396647-1cc4-4a99-892b-b42ee1d82423
2025-09-19 15:17:30,988 - database_manager - INFO - 成功删除vendor联系方式: 0f396647-1cc4-4a99-892b-b42ee1d82423
2025-09-19 15:17:30,991 - __main__ - INFO - 成功处理supplier: 100110
2025-09-19 15:17:30,992 - __main__ - INFO - 进度: 112/215
2025-09-19 15:17:30,992 - __main__ - INFO - 处理supplier: 100111
2025-09-19 15:17:30,994 - __main__ - INFO - 找到对应vendor: 84d85c3e-3994-447c-8407-1f6444a73229
2025-09-19 15:17:30,995 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:30,995 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:30,997 - database_manager - INFO - 成功更新vendor: 84d85c3e-3994-447c-8407-1f6444a73229
2025-09-19 15:17:30,998 - __main__ - INFO - 成功更新vendor: 84d85c3e-3994-447c-8407-1f6444a73229
2025-09-19 15:17:31,001 - database_manager - INFO - 成功删除vendor联系方式: 84d85c3e-3994-447c-8407-1f6444a73229
2025-09-19 15:17:31,012 - __main__ - INFO - 成功处理supplier: 100111
2025-09-19 15:17:31,012 - __main__ - INFO - 进度: 113/215
2025-09-19 15:17:31,012 - __main__ - INFO - 处理supplier: 100112
2025-09-19 15:17:31,014 - __main__ - INFO - 找到对应vendor: 5692de00-b42a-4e3e-a4d4-7a3c9cf8e5fa
2025-09-19 15:17:31,015 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:31,015 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,016 - database_manager - INFO - 成功更新vendor: 5692de00-b42a-4e3e-a4d4-7a3c9cf8e5fa
2025-09-19 15:17:31,017 - __main__ - INFO - 成功更新vendor: 5692de00-b42a-4e3e-a4d4-7a3c9cf8e5fa
2025-09-19 15:17:31,021 - database_manager - INFO - 成功删除vendor联系方式: 5692de00-b42a-4e3e-a4d4-7a3c9cf8e5fa
2025-09-19 15:17:31,026 - __main__ - INFO - 成功处理supplier: 100112
2025-09-19 15:17:31,026 - __main__ - INFO - 进度: 114/215
2025-09-19 15:17:31,026 - __main__ - INFO - 处理supplier: 100113
2025-09-19 15:17:31,027 - __main__ - WARNING - 未找到对应的vendor，跳过: 100113
2025-09-19 15:17:31,028 - __main__ - INFO - 进度: 115/215
2025-09-19 15:17:31,028 - __main__ - INFO - 处理supplier: 100114
2025-09-19 15:17:31,029 - __main__ - INFO - 找到对应vendor: 413d2b3c-009a-4c03-b760-dee054417ea2
2025-09-19 15:17:31,029 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:31,029 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,030 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,031 - database_manager - INFO - 成功更新vendor: 413d2b3c-009a-4c03-b760-dee054417ea2
2025-09-19 15:17:31,031 - __main__ - INFO - 成功更新vendor: 413d2b3c-009a-4c03-b760-dee054417ea2
2025-09-19 15:17:31,034 - database_manager - INFO - 成功删除vendor联系方式: 413d2b3c-009a-4c03-b760-dee054417ea2
2025-09-19 15:17:31,038 - __main__ - INFO - 成功处理supplier: 100114
2025-09-19 15:17:31,038 - __main__ - INFO - 进度: 116/215
2025-09-19 15:17:31,038 - __main__ - INFO - 处理supplier: 100115
2025-09-19 15:17:31,039 - __main__ - INFO - 找到对应vendor: 4aab9977-a25b-4b7b-b410-dd218e6ab292
2025-09-19 15:17:31,039 - database_manager - INFO - 成功更新vendor: 4aab9977-a25b-4b7b-b410-dd218e6ab292
2025-09-19 15:17:31,040 - __main__ - INFO - 成功更新vendor: 4aab9977-a25b-4b7b-b410-dd218e6ab292
2025-09-19 15:17:31,043 - database_manager - INFO - 成功删除vendor联系方式: 4aab9977-a25b-4b7b-b410-dd218e6ab292
2025-09-19 15:17:31,044 - __main__ - INFO - 成功处理supplier: 100115
2025-09-19 15:17:31,044 - __main__ - INFO - 进度: 117/215
2025-09-19 15:17:31,045 - __main__ - INFO - 处理supplier: 100116
2025-09-19 15:17:31,046 - __main__ - WARNING - 未找到对应的vendor，跳过: 100116
2025-09-19 15:17:31,046 - __main__ - INFO - 进度: 118/215
2025-09-19 15:17:31,046 - __main__ - INFO - 处理supplier: 100117
2025-09-19 15:17:31,047 - __main__ - WARNING - 未找到对应的vendor，跳过: 100117
2025-09-19 15:17:31,047 - __main__ - INFO - 进度: 119/215
2025-09-19 15:17:31,048 - __main__ - INFO - 处理supplier: 100118
2025-09-19 15:17:31,049 - __main__ - INFO - 找到对应vendor: 6ad7e357-7cd9-4d30-a6bd-063d4006c70f
2025-09-19 15:17:31,051 - database_manager - INFO - 成功删除vendor联系方式: 6ad7e357-7cd9-4d30-a6bd-063d4006c70f
2025-09-19 15:17:31,052 - __main__ - INFO - 成功处理supplier: 100118
2025-09-19 15:17:31,052 - __main__ - INFO - 进度: 120/215
2025-09-19 15:17:31,052 - __main__ - INFO - 处理supplier: 100119
2025-09-19 15:17:31,052 - __main__ - INFO - 找到对应vendor: 267b9430-07c0-40ae-9d43-90a2535fb6cb
2025-09-19 15:17:31,053 - database_manager - INFO - 成功更新vendor: 267b9430-07c0-40ae-9d43-90a2535fb6cb
2025-09-19 15:17:31,054 - __main__ - INFO - 成功更新vendor: 267b9430-07c0-40ae-9d43-90a2535fb6cb
2025-09-19 15:17:31,055 - database_manager - INFO - 成功删除vendor联系方式: 267b9430-07c0-40ae-9d43-90a2535fb6cb
2025-09-19 15:17:31,059 - __main__ - INFO - 成功处理supplier: 100119
2025-09-19 15:17:31,059 - __main__ - INFO - 进度: 121/215
2025-09-19 15:17:31,059 - __main__ - INFO - 处理supplier: 100120
2025-09-19 15:17:31,060 - __main__ - INFO - 找到对应vendor: 39d71d29-d064-4466-89e4-e2fd70627970
2025-09-19 15:17:31,060 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:31,060 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,062 - database_manager - INFO - 成功更新vendor: 39d71d29-d064-4466-89e4-e2fd70627970
2025-09-19 15:17:31,063 - __main__ - INFO - 成功更新vendor: 39d71d29-d064-4466-89e4-e2fd70627970
2025-09-19 15:17:31,065 - database_manager - INFO - 成功删除vendor联系方式: 39d71d29-d064-4466-89e4-e2fd70627970
2025-09-19 15:17:31,068 - __main__ - INFO - 成功处理supplier: 100120
2025-09-19 15:17:31,068 - __main__ - INFO - 进度: 122/215
2025-09-19 15:17:31,068 - __main__ - INFO - 处理supplier: 100121
2025-09-19 15:17:31,069 - __main__ - WARNING - 未找到对应的vendor，跳过: 100121
2025-09-19 15:17:31,069 - __main__ - INFO - 进度: 123/215
2025-09-19 15:17:31,069 - __main__ - INFO - 处理supplier: 100122
2025-09-19 15:17:31,070 - __main__ - WARNING - 未找到对应的vendor，跳过: 100122
2025-09-19 15:17:31,070 - __main__ - INFO - 进度: 124/215
2025-09-19 15:17:31,070 - __main__ - INFO - 处理supplier: 100123
2025-09-19 15:17:31,071 - __main__ - WARNING - 未找到对应的vendor，跳过: 100123
2025-09-19 15:17:31,071 - __main__ - INFO - 进度: 125/215
2025-09-19 15:17:31,071 - __main__ - INFO - 处理supplier: 100124
2025-09-19 15:17:31,072 - __main__ - WARNING - 未找到对应的vendor，跳过: 100124
2025-09-19 15:17:31,072 - __main__ - INFO - 进度: 126/215
2025-09-19 15:17:31,072 - __main__ - INFO - 处理supplier: 100125
2025-09-19 15:17:31,072 - __main__ - WARNING - 未找到对应的vendor，跳过: 100125
2025-09-19 15:17:31,072 - __main__ - INFO - 进度: 127/215
2025-09-19 15:17:31,072 - __main__ - INFO - 处理supplier: 100126
2025-09-19 15:17:31,074 - __main__ - WARNING - 未找到对应的vendor，跳过: 100126
2025-09-19 15:17:31,074 - __main__ - INFO - 进度: 128/215
2025-09-19 15:17:31,074 - __main__ - INFO - 处理supplier: 100127
2025-09-19 15:17:31,076 - __main__ - WARNING - 未找到对应的vendor，跳过: 100127
2025-09-19 15:17:31,076 - __main__ - INFO - 进度: 129/215
2025-09-19 15:17:31,076 - __main__ - INFO - 处理supplier: 100128
2025-09-19 15:17:31,078 - __main__ - WARNING - 未找到对应的vendor，跳过: 100128
2025-09-19 15:17:31,078 - __main__ - INFO - 进度: 130/215
2025-09-19 15:17:31,078 - __main__ - INFO - 处理supplier: 100129
2025-09-19 15:17:31,080 - __main__ - WARNING - 未找到对应的vendor，跳过: 100129
2025-09-19 15:17:31,080 - __main__ - INFO - 进度: 131/215
2025-09-19 15:17:31,080 - __main__ - INFO - 处理supplier: 100130
2025-09-19 15:17:31,083 - __main__ - WARNING - 未找到对应的vendor，跳过: 100130
2025-09-19 15:17:31,083 - __main__ - INFO - 进度: 132/215
2025-09-19 15:17:31,083 - __main__ - INFO - 处理supplier: 100131
2025-09-19 15:17:31,085 - __main__ - WARNING - 未找到对应的vendor，跳过: 100131
2025-09-19 15:17:31,085 - __main__ - INFO - 进度: 133/215
2025-09-19 15:17:31,085 - __main__ - INFO - 处理supplier: 100132
2025-09-19 15:17:31,086 - __main__ - WARNING - 未找到对应的vendor，跳过: 100132
2025-09-19 15:17:31,086 - __main__ - INFO - 进度: 134/215
2025-09-19 15:17:31,086 - __main__ - INFO - 处理supplier: 100133
2025-09-19 15:17:31,087 - __main__ - WARNING - 未找到对应的vendor，跳过: 100133
2025-09-19 15:17:31,087 - __main__ - INFO - 进度: 135/215
2025-09-19 15:17:31,088 - __main__ - INFO - 处理supplier: 100134
2025-09-19 15:17:31,089 - __main__ - WARNING - 未找到对应的vendor，跳过: 100134
2025-09-19 15:17:31,089 - __main__ - INFO - 进度: 136/215
2025-09-19 15:17:31,089 - __main__ - INFO - 处理supplier: 100135
2025-09-19 15:17:31,093 - __main__ - WARNING - 未找到对应的vendor，跳过: 100135
2025-09-19 15:17:31,093 - __main__ - INFO - 进度: 137/215
2025-09-19 15:17:31,093 - __main__ - INFO - 处理supplier: 100136
2025-09-19 15:17:31,096 - __main__ - WARNING - 未找到对应的vendor，跳过: 100136
2025-09-19 15:17:31,096 - __main__ - INFO - 进度: 138/215
2025-09-19 15:17:31,096 - __main__ - INFO - 处理supplier: 100137
2025-09-19 15:17:31,100 - __main__ - INFO - 找到对应vendor: fcf5cfc1-6706-408a-897e-120815c1e0bb
2025-09-19 15:17:31,101 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,101 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:31,107 - database_manager - INFO - 成功更新vendor: fcf5cfc1-6706-408a-897e-120815c1e0bb
2025-09-19 15:17:31,109 - __main__ - INFO - 成功更新vendor: fcf5cfc1-6706-408a-897e-120815c1e0bb
2025-09-19 15:17:31,114 - database_manager - INFO - 成功删除vendor联系方式: fcf5cfc1-6706-408a-897e-120815c1e0bb
2025-09-19 15:17:31,126 - __main__ - INFO - 成功处理supplier: 100137
2025-09-19 15:17:31,126 - __main__ - INFO - 进度: 139/215
2025-09-19 15:17:31,126 - __main__ - INFO - 处理supplier: 100138
2025-09-19 15:17:31,130 - __main__ - WARNING - 未找到对应的vendor，跳过: 100138
2025-09-19 15:17:31,130 - __main__ - INFO - 进度: 140/215
2025-09-19 15:17:31,130 - __main__ - INFO - 处理supplier: 100139
2025-09-19 15:17:31,133 - __main__ - WARNING - 未找到对应的vendor，跳过: 100139
2025-09-19 15:17:31,133 - __main__ - INFO - 进度: 141/215
2025-09-19 15:17:31,134 - __main__ - INFO - 处理supplier: 100140
2025-09-19 15:17:31,136 - __main__ - WARNING - 未找到对应的vendor，跳过: 100140
2025-09-19 15:17:31,137 - __main__ - INFO - 进度: 142/215
2025-09-19 15:17:31,137 - __main__ - INFO - 处理supplier: 100141
2025-09-19 15:17:31,138 - __main__ - WARNING - 未找到对应的vendor，跳过: 100141
2025-09-19 15:17:31,138 - __main__ - INFO - 进度: 143/215
2025-09-19 15:17:31,138 - __main__ - INFO - 处理supplier: 100142
2025-09-19 15:17:31,142 - __main__ - WARNING - 未找到对应的vendor，跳过: 100142
2025-09-19 15:17:31,142 - __main__ - INFO - 进度: 144/215
2025-09-19 15:17:31,142 - __main__ - INFO - 处理supplier: 100143
2025-09-19 15:17:31,145 - __main__ - WARNING - 未找到对应的vendor，跳过: 100143
2025-09-19 15:17:31,145 - __main__ - INFO - 进度: 145/215
2025-09-19 15:17:31,145 - __main__ - INFO - 处理supplier: 100144
2025-09-19 15:17:31,150 - __main__ - WARNING - 未找到对应的vendor，跳过: 100144
2025-09-19 15:17:31,150 - __main__ - INFO - 进度: 146/215
2025-09-19 15:17:31,150 - __main__ - INFO - 处理supplier: 100145
2025-09-19 15:17:31,153 - __main__ - WARNING - 未找到对应的vendor，跳过: 100145
2025-09-19 15:17:31,153 - __main__ - INFO - 进度: 147/215
2025-09-19 15:17:31,153 - __main__ - INFO - 处理supplier: 100146
2025-09-19 15:17:31,154 - __main__ - INFO - 找到对应vendor: ca065074-115c-4880-a487-e9df68a60c03
2025-09-19 15:17:31,160 - database_manager - INFO - 成功删除vendor联系方式: ca065074-115c-4880-a487-e9df68a60c03
2025-09-19 15:17:31,166 - __main__ - INFO - 成功处理supplier: 100146
2025-09-19 15:17:31,167 - __main__ - INFO - 进度: 148/215
2025-09-19 15:17:31,167 - __main__ - INFO - 处理supplier: 100147
2025-09-19 15:17:31,170 - __main__ - WARNING - 未找到对应的vendor，跳过: 100147
2025-09-19 15:17:31,170 - __main__ - INFO - 进度: 149/215
2025-09-19 15:17:31,170 - __main__ - INFO - 处理supplier: 100148
2025-09-19 15:17:31,173 - __main__ - INFO - 找到对应vendor: c3606020-01ea-45b0-b8f9-1d81055fcee5
2025-09-19 15:17:31,173 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,176 - database_manager - INFO - 成功更新vendor: c3606020-01ea-45b0-b8f9-1d81055fcee5
2025-09-19 15:17:31,177 - __main__ - INFO - 成功更新vendor: c3606020-01ea-45b0-b8f9-1d81055fcee5
2025-09-19 15:17:31,182 - database_manager - INFO - 成功删除vendor联系方式: c3606020-01ea-45b0-b8f9-1d81055fcee5
2025-09-19 15:17:31,188 - __main__ - INFO - 成功处理supplier: 100148
2025-09-19 15:17:31,188 - __main__ - INFO - 进度: 150/215
2025-09-19 15:17:31,188 - __main__ - INFO - 处理supplier: 100149
2025-09-19 15:17:31,189 - __main__ - INFO - 找到对应vendor: 750a7abd-6021-4a0f-ab55-5dc19670890a
2025-09-19 15:17:31,189 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:31,189 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,190 - database_manager - INFO - 成功更新vendor: 750a7abd-6021-4a0f-ab55-5dc19670890a
2025-09-19 15:17:31,191 - __main__ - INFO - 成功更新vendor: 750a7abd-6021-4a0f-ab55-5dc19670890a
2025-09-19 15:17:31,194 - database_manager - INFO - 成功删除vendor联系方式: 750a7abd-6021-4a0f-ab55-5dc19670890a
2025-09-19 15:17:31,203 - __main__ - INFO - 成功处理supplier: 100149
2025-09-19 15:17:31,203 - __main__ - INFO - 进度: 151/215
2025-09-19 15:17:31,203 - __main__ - INFO - 处理supplier: 100150
2025-09-19 15:17:31,204 - __main__ - INFO - 找到对应vendor: 8d319517-f804-4c68-a482-1830005dcdb2
2025-09-19 15:17:31,207 - database_manager - INFO - 成功删除vendor联系方式: 8d319517-f804-4c68-a482-1830005dcdb2
2025-09-19 15:17:31,208 - __main__ - INFO - 成功处理supplier: 100150
2025-09-19 15:17:31,208 - __main__ - INFO - 进度: 152/215
2025-09-19 15:17:31,208 - __main__ - INFO - 处理supplier: 100151
2025-09-19 15:17:31,210 - __main__ - WARNING - 未找到对应的vendor，跳过: 100151
2025-09-19 15:17:31,210 - __main__ - INFO - 进度: 153/215
2025-09-19 15:17:31,210 - __main__ - INFO - 处理supplier: 100152
2025-09-19 15:17:31,212 - __main__ - INFO - 找到对应vendor: 9ee096e0-1600-4051-afce-d9a84e431c73
2025-09-19 15:17:31,214 - database_manager - INFO - 成功更新vendor: 9ee096e0-1600-4051-afce-d9a84e431c73
2025-09-19 15:17:31,214 - __main__ - INFO - 成功更新vendor: 9ee096e0-1600-4051-afce-d9a84e431c73
2025-09-19 15:17:31,216 - database_manager - INFO - 成功删除vendor联系方式: 9ee096e0-1600-4051-afce-d9a84e431c73
2025-09-19 15:17:31,217 - __main__ - INFO - 成功处理supplier: 100152
2025-09-19 15:17:31,217 - __main__ - INFO - 进度: 154/215
2025-09-19 15:17:31,217 - __main__ - INFO - 处理supplier: 100153
2025-09-19 15:17:31,218 - __main__ - INFO - 找到对应vendor: 270eca61-fc1b-4282-9f09-c6a3a9a5bf22
2025-09-19 15:17:31,218 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:31,218 - database_manager - INFO - 成功更新vendor: 270eca61-fc1b-4282-9f09-c6a3a9a5bf22
2025-09-19 15:17:31,219 - __main__ - INFO - 成功更新vendor: 270eca61-fc1b-4282-9f09-c6a3a9a5bf22
2025-09-19 15:17:31,220 - database_manager - INFO - 成功删除vendor联系方式: 270eca61-fc1b-4282-9f09-c6a3a9a5bf22
2025-09-19 15:17:31,222 - __main__ - INFO - 成功处理supplier: 100153
2025-09-19 15:17:31,222 - __main__ - INFO - 进度: 155/215
2025-09-19 15:17:31,222 - __main__ - INFO - 处理supplier: 100154
2025-09-19 15:17:31,223 - __main__ - INFO - 找到对应vendor: c2cd61a5-9894-40af-8e21-b9aefb8f20d4
2025-09-19 15:17:31,225 - database_manager - INFO - 成功删除vendor联系方式: c2cd61a5-9894-40af-8e21-b9aefb8f20d4
2025-09-19 15:17:31,226 - __main__ - INFO - 成功处理supplier: 100154
2025-09-19 15:17:31,226 - __main__ - INFO - 进度: 156/215
2025-09-19 15:17:31,226 - __main__ - INFO - 处理supplier: 100155
2025-09-19 15:17:31,227 - __main__ - WARNING - 未找到对应的vendor，跳过: 100155
2025-09-19 15:17:31,227 - __main__ - INFO - 进度: 157/215
2025-09-19 15:17:31,227 - __main__ - INFO - 处理supplier: 100156
2025-09-19 15:17:31,228 - __main__ - INFO - 找到对应vendor: 420e3688-81bd-4d77-8b9d-d4733fa1da55
2025-09-19 15:17:31,229 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:31,230 - database_manager - INFO - 成功更新vendor: 420e3688-81bd-4d77-8b9d-d4733fa1da55
2025-09-19 15:17:31,230 - __main__ - INFO - 成功更新vendor: 420e3688-81bd-4d77-8b9d-d4733fa1da55
2025-09-19 15:17:31,232 - database_manager - INFO - 成功删除vendor联系方式: 420e3688-81bd-4d77-8b9d-d4733fa1da55
2025-09-19 15:17:31,234 - __main__ - INFO - 成功处理supplier: 100156
2025-09-19 15:17:31,234 - __main__ - INFO - 进度: 158/215
2025-09-19 15:17:31,234 - __main__ - INFO - 处理supplier: 100157
2025-09-19 15:17:31,234 - __main__ - WARNING - 未找到对应的vendor，跳过: 100157
2025-09-19 15:17:31,235 - __main__ - INFO - 进度: 159/215
2025-09-19 15:17:31,235 - __main__ - INFO - 处理supplier: 100158
2025-09-19 15:17:31,235 - __main__ - INFO - 找到对应vendor: ce8d2a5e-ec22-4d6f-9e07-eb59e811ee0f
2025-09-19 15:17:31,236 - database_manager - INFO - 成功删除vendor联系方式: ce8d2a5e-ec22-4d6f-9e07-eb59e811ee0f
2025-09-19 15:17:31,237 - __main__ - INFO - 成功处理supplier: 100158
2025-09-19 15:17:31,237 - __main__ - INFO - 进度: 160/215
2025-09-19 15:17:31,237 - __main__ - INFO - 处理supplier: 100159
2025-09-19 15:17:31,238 - __main__ - WARNING - 未找到对应的vendor，跳过: 100159
2025-09-19 15:17:31,238 - __main__ - INFO - 进度: 161/215
2025-09-19 15:17:31,238 - __main__ - INFO - 处理supplier: 100160
2025-09-19 15:17:31,238 - __main__ - INFO - 找到对应vendor: 257a30a3-bdc6-44d5-b689-e5dacbcb5e6a
2025-09-19 15:17:31,238 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,239 - database_manager - INFO - 成功更新vendor: 257a30a3-bdc6-44d5-b689-e5dacbcb5e6a
2025-09-19 15:17:31,240 - __main__ - INFO - 成功更新vendor: 257a30a3-bdc6-44d5-b689-e5dacbcb5e6a
2025-09-19 15:17:31,241 - database_manager - INFO - 成功删除vendor联系方式: 257a30a3-bdc6-44d5-b689-e5dacbcb5e6a
2025-09-19 15:17:31,246 - __main__ - INFO - 成功处理supplier: 100160
2025-09-19 15:17:31,247 - __main__ - INFO - 进度: 162/215
2025-09-19 15:17:31,247 - __main__ - INFO - 处理supplier: 100161
2025-09-19 15:17:31,247 - __main__ - INFO - 找到对应vendor: f88ef61f-c8da-42d2-8ff9-fb22b5277e54
2025-09-19 15:17:31,247 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,247 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:31,248 - database_manager - INFO - 成功更新vendor: f88ef61f-c8da-42d2-8ff9-fb22b5277e54
2025-09-19 15:17:31,248 - __main__ - INFO - 成功更新vendor: f88ef61f-c8da-42d2-8ff9-fb22b5277e54
2025-09-19 15:17:31,249 - database_manager - INFO - 成功删除vendor联系方式: f88ef61f-c8da-42d2-8ff9-fb22b5277e54
2025-09-19 15:17:31,252 - __main__ - INFO - 成功处理supplier: 100161
2025-09-19 15:17:31,252 - __main__ - INFO - 进度: 163/215
2025-09-19 15:17:31,252 - __main__ - INFO - 处理supplier: 100162
2025-09-19 15:17:31,253 - __main__ - WARNING - 未找到对应的vendor，跳过: 100162
2025-09-19 15:17:31,253 - __main__ - INFO - 进度: 164/215
2025-09-19 15:17:31,253 - __main__ - INFO - 处理supplier: 100163
2025-09-19 15:17:31,253 - __main__ - WARNING - 未找到对应的vendor，跳过: 100163
2025-09-19 15:17:31,253 - __main__ - INFO - 进度: 165/215
2025-09-19 15:17:31,253 - __main__ - INFO - 处理supplier: 100164
2025-09-19 15:17:31,254 - __main__ - WARNING - 未找到对应的vendor，跳过: 100164
2025-09-19 15:17:31,254 - __main__ - INFO - 进度: 166/215
2025-09-19 15:17:31,254 - __main__ - INFO - 处理supplier: 100165
2025-09-19 15:17:31,254 - __main__ - WARNING - 未找到对应的vendor，跳过: 100165
2025-09-19 15:17:31,255 - __main__ - INFO - 进度: 167/215
2025-09-19 15:17:31,255 - __main__ - INFO - 处理supplier: 100166
2025-09-19 15:17:31,255 - __main__ - WARNING - 未找到对应的vendor，跳过: 100166
2025-09-19 15:17:31,255 - __main__ - INFO - 进度: 168/215
2025-09-19 15:17:31,255 - __main__ - INFO - 处理supplier: 100167
2025-09-19 15:17:31,256 - __main__ - WARNING - 未找到对应的vendor，跳过: 100167
2025-09-19 15:17:31,256 - __main__ - INFO - 进度: 169/215
2025-09-19 15:17:31,256 - __main__ - INFO - 处理supplier: 100168
2025-09-19 15:17:31,257 - __main__ - WARNING - 未找到对应的vendor，跳过: 100168
2025-09-19 15:17:31,257 - __main__ - INFO - 进度: 170/215
2025-09-19 15:17:31,257 - __main__ - INFO - 处理supplier: 100169
2025-09-19 15:17:31,258 - __main__ - INFO - 找到对应vendor: 33610208-e0a2-4ff2-8ca7-79415d2cdfca
2025-09-19 15:17:31,259 - database_manager - INFO - 成功删除vendor联系方式: 33610208-e0a2-4ff2-8ca7-79415d2cdfca
2025-09-19 15:17:31,260 - __main__ - INFO - 成功处理supplier: 100169
2025-09-19 15:17:31,261 - __main__ - INFO - 进度: 171/215
2025-09-19 15:17:31,261 - __main__ - INFO - 处理supplier: 100170
2025-09-19 15:17:31,262 - __main__ - WARNING - 未找到对应的vendor，跳过: 100170
2025-09-19 15:17:31,262 - __main__ - INFO - 进度: 172/215
2025-09-19 15:17:31,262 - __main__ - INFO - 处理supplier: 100171
2025-09-19 15:17:31,263 - __main__ - WARNING - 未找到对应的vendor，跳过: 100171
2025-09-19 15:17:31,263 - __main__ - INFO - 进度: 173/215
2025-09-19 15:17:31,263 - __main__ - INFO - 处理supplier: 100172
2025-09-19 15:17:31,264 - __main__ - WARNING - 未找到对应的vendor，跳过: 100172
2025-09-19 15:17:31,264 - __main__ - INFO - 进度: 174/215
2025-09-19 15:17:31,264 - __main__ - INFO - 处理supplier: 100173
2025-09-19 15:17:31,265 - __main__ - INFO - 找到对应vendor: fd7b77bf-f417-41db-8ff7-0164327f6b4d
2025-09-19 15:17:31,265 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,265 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,265 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,265 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:31,266 - database_manager - INFO - 成功更新vendor: fd7b77bf-f417-41db-8ff7-0164327f6b4d
2025-09-19 15:17:31,266 - __main__ - INFO - 成功更新vendor: fd7b77bf-f417-41db-8ff7-0164327f6b4d
2025-09-19 15:17:31,267 - database_manager - INFO - 成功删除vendor联系方式: fd7b77bf-f417-41db-8ff7-0164327f6b4d
2025-09-19 15:17:31,271 - __main__ - INFO - 成功处理supplier: 100173
2025-09-19 15:17:31,271 - __main__ - INFO - 进度: 175/215
2025-09-19 15:17:31,271 - __main__ - INFO - 处理supplier: 100174
2025-09-19 15:17:31,272 - __main__ - INFO - 找到对应vendor: 2b8a46bd-9bdd-4723-a2a4-2dffa8dfc20c
2025-09-19 15:17:31,273 - database_manager - INFO - 成功删除vendor联系方式: 2b8a46bd-9bdd-4723-a2a4-2dffa8dfc20c
2025-09-19 15:17:31,275 - __main__ - INFO - 成功处理supplier: 100174
2025-09-19 15:17:31,275 - __main__ - INFO - 进度: 176/215
2025-09-19 15:17:31,275 - __main__ - INFO - 处理supplier: 100175
2025-09-19 15:17:31,276 - __main__ - INFO - 找到对应vendor: 50c427a9-e5d3-4742-8fad-7b8d2ade0a86
2025-09-19 15:17:31,278 - database_manager - INFO - 成功删除vendor联系方式: 50c427a9-e5d3-4742-8fad-7b8d2ade0a86
2025-09-19 15:17:31,279 - __main__ - INFO - 成功处理supplier: 100175
2025-09-19 15:17:31,279 - __main__ - INFO - 进度: 177/215
2025-09-19 15:17:31,279 - __main__ - INFO - 处理supplier: 100176
2025-09-19 15:17:31,280 - __main__ - INFO - 找到对应vendor: 22d09107-ca3e-4364-aa39-c6a48c1c4220
2025-09-19 15:17:31,280 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:31,281 - database_manager - INFO - 成功更新vendor: 22d09107-ca3e-4364-aa39-c6a48c1c4220
2025-09-19 15:17:31,281 - __main__ - INFO - 成功更新vendor: 22d09107-ca3e-4364-aa39-c6a48c1c4220
2025-09-19 15:17:31,283 - database_manager - INFO - 成功删除vendor联系方式: 22d09107-ca3e-4364-aa39-c6a48c1c4220
2025-09-19 15:17:31,285 - __main__ - INFO - 成功处理supplier: 100176
2025-09-19 15:17:31,285 - __main__ - INFO - 进度: 178/215
2025-09-19 15:17:31,285 - __main__ - INFO - 处理supplier: 100177
2025-09-19 15:17:31,286 - __main__ - INFO - 找到对应vendor: 0aa47e1f-8f30-4256-9de9-f302e700bc1d
2025-09-19 15:17:31,287 - database_manager - INFO - 成功更新vendor: 0aa47e1f-8f30-4256-9de9-f302e700bc1d
2025-09-19 15:17:31,287 - __main__ - INFO - 成功更新vendor: 0aa47e1f-8f30-4256-9de9-f302e700bc1d
2025-09-19 15:17:31,288 - database_manager - INFO - 成功删除vendor联系方式: 0aa47e1f-8f30-4256-9de9-f302e700bc1d
2025-09-19 15:17:31,289 - __main__ - INFO - 成功处理supplier: 100177
2025-09-19 15:17:31,289 - __main__ - INFO - 进度: 179/215
2025-09-19 15:17:31,289 - __main__ - INFO - 处理supplier: 100178
2025-09-19 15:17:31,290 - __main__ - WARNING - 未找到对应的vendor，跳过: 100178
2025-09-19 15:17:31,290 - __main__ - INFO - 进度: 180/215
2025-09-19 15:17:31,290 - __main__ - INFO - 处理supplier: 100179
2025-09-19 15:17:31,291 - __main__ - WARNING - 未找到对应的vendor，跳过: 100179
2025-09-19 15:17:31,291 - __main__ - INFO - 进度: 181/215
2025-09-19 15:17:31,291 - __main__ - INFO - 处理supplier: 100180
2025-09-19 15:17:31,291 - __main__ - WARNING - 未找到对应的vendor，跳过: 100180
2025-09-19 15:17:31,291 - __main__ - INFO - 进度: 182/215
2025-09-19 15:17:31,292 - __main__ - INFO - 处理supplier: 100181
2025-09-19 15:17:31,293 - __main__ - INFO - 找到对应vendor: 932a5b43-3abd-4186-91f3-5a2c571aaafb
2025-09-19 15:17:31,293 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:31,294 - database_manager - INFO - 成功更新vendor: 932a5b43-3abd-4186-91f3-5a2c571aaafb
2025-09-19 15:17:31,295 - __main__ - INFO - 成功更新vendor: 932a5b43-3abd-4186-91f3-5a2c571aaafb
2025-09-19 15:17:31,297 - database_manager - INFO - 成功删除vendor联系方式: 932a5b43-3abd-4186-91f3-5a2c571aaafb
2025-09-19 15:17:31,299 - __main__ - INFO - 成功处理supplier: 100181
2025-09-19 15:17:31,299 - __main__ - INFO - 进度: 183/215
2025-09-19 15:17:31,299 - __main__ - INFO - 处理supplier: 100182
2025-09-19 15:17:31,300 - __main__ - INFO - 找到对应vendor: 28b6a6f3-1a24-48ab-9620-22c6e68f967f
2025-09-19 15:17:31,300 - database_manager - INFO - 成功更新vendor: 28b6a6f3-1a24-48ab-9620-22c6e68f967f
2025-09-19 15:17:31,301 - __main__ - INFO - 成功更新vendor: 28b6a6f3-1a24-48ab-9620-22c6e68f967f
2025-09-19 15:17:31,302 - database_manager - INFO - 成功删除vendor联系方式: 28b6a6f3-1a24-48ab-9620-22c6e68f967f
2025-09-19 15:17:31,303 - __main__ - INFO - 成功处理supplier: 100182
2025-09-19 15:17:31,303 - __main__ - INFO - 进度: 184/215
2025-09-19 15:17:31,303 - __main__ - INFO - 处理supplier: 100183
2025-09-19 15:17:31,304 - __main__ - INFO - 找到对应vendor: d69eba4c-804f-4289-a2d5-229bf9965971
2025-09-19 15:17:31,304 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:31,304 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,304 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,304 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,304 - database_manager - INFO - 成功更新vendor: d69eba4c-804f-4289-a2d5-229bf9965971
2025-09-19 15:17:31,305 - __main__ - INFO - 成功更新vendor: d69eba4c-804f-4289-a2d5-229bf9965971
2025-09-19 15:17:31,306 - database_manager - INFO - 成功删除vendor联系方式: d69eba4c-804f-4289-a2d5-229bf9965971
2025-09-19 15:17:31,308 - __main__ - INFO - 成功处理supplier: 100183
2025-09-19 15:17:31,308 - __main__ - INFO - 进度: 185/215
2025-09-19 15:17:31,308 - __main__ - INFO - 处理supplier: 100184
2025-09-19 15:17:31,309 - __main__ - INFO - 找到对应vendor: eefd491c-ead8-432e-b3c8-eb1dab54acfc
2025-09-19 15:17:31,309 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:31,309 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,310 - database_manager - INFO - 成功更新vendor: eefd491c-ead8-432e-b3c8-eb1dab54acfc
2025-09-19 15:17:31,311 - __main__ - INFO - 成功更新vendor: eefd491c-ead8-432e-b3c8-eb1dab54acfc
2025-09-19 15:17:31,312 - database_manager - INFO - 成功删除vendor联系方式: eefd491c-ead8-432e-b3c8-eb1dab54acfc
2025-09-19 15:17:31,314 - __main__ - INFO - 成功处理supplier: 100184
2025-09-19 15:17:31,314 - __main__ - INFO - 进度: 186/215
2025-09-19 15:17:31,314 - __main__ - INFO - 处理supplier: 100185
2025-09-19 15:17:31,315 - __main__ - INFO - 找到对应vendor: d84a956f-576f-447f-b327-85ae5d76aa9f
2025-09-19 15:17:31,315 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:31,315 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,315 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,315 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,316 - database_manager - INFO - 成功更新vendor: d84a956f-576f-447f-b327-85ae5d76aa9f
2025-09-19 15:17:31,316 - __main__ - INFO - 成功更新vendor: d84a956f-576f-447f-b327-85ae5d76aa9f
2025-09-19 15:17:31,317 - database_manager - INFO - 成功删除vendor联系方式: d84a956f-576f-447f-b327-85ae5d76aa9f
2025-09-19 15:17:31,319 - __main__ - INFO - 成功处理supplier: 100185
2025-09-19 15:17:31,319 - __main__ - INFO - 进度: 187/215
2025-09-19 15:17:31,319 - __main__ - INFO - 处理supplier: 100186
2025-09-19 15:17:31,319 - __main__ - INFO - 找到对应vendor: a427db77-4706-4f6e-b3cd-ce482ebb5a86
2025-09-19 15:17:31,320 - database_manager - INFO - 成功更新vendor: a427db77-4706-4f6e-b3cd-ce482ebb5a86
2025-09-19 15:17:31,320 - __main__ - INFO - 成功更新vendor: a427db77-4706-4f6e-b3cd-ce482ebb5a86
2025-09-19 15:17:31,321 - database_manager - INFO - 成功删除vendor联系方式: a427db77-4706-4f6e-b3cd-ce482ebb5a86
2025-09-19 15:17:31,322 - __main__ - INFO - 成功处理supplier: 100186
2025-09-19 15:17:31,322 - __main__ - INFO - 进度: 188/215
2025-09-19 15:17:31,322 - __main__ - INFO - 处理supplier: 100187
2025-09-19 15:17:31,322 - __main__ - INFO - 找到对应vendor: 89f85b7d-aed3-4355-acd5-b8b55ac93f4d
2025-09-19 15:17:31,323 - database_manager - INFO - 成功删除vendor联系方式: 89f85b7d-aed3-4355-acd5-b8b55ac93f4d
2025-09-19 15:17:31,324 - __main__ - INFO - 成功处理supplier: 100187
2025-09-19 15:17:31,324 - __main__ - INFO - 进度: 189/215
2025-09-19 15:17:31,324 - __main__ - INFO - 处理supplier: 100188
2025-09-19 15:17:31,325 - __main__ - INFO - 找到对应vendor: 7456d2ca-ee61-4c06-ab37-45579958f001
2025-09-19 15:17:31,327 - database_manager - INFO - 成功删除vendor联系方式: 7456d2ca-ee61-4c06-ab37-45579958f001
2025-09-19 15:17:31,328 - __main__ - INFO - 成功处理supplier: 100188
2025-09-19 15:17:31,328 - __main__ - INFO - 进度: 190/215
2025-09-19 15:17:31,328 - __main__ - INFO - 处理supplier: 100189
2025-09-19 15:17:31,328 - __main__ - INFO - 找到对应vendor: faa4a421-3239-44c7-8626-bb89b9614341
2025-09-19 15:17:31,329 - database_manager - INFO - 成功更新vendor: faa4a421-3239-44c7-8626-bb89b9614341
2025-09-19 15:17:31,329 - __main__ - INFO - 成功更新vendor: faa4a421-3239-44c7-8626-bb89b9614341
2025-09-19 15:17:31,330 - database_manager - INFO - 成功删除vendor联系方式: faa4a421-3239-44c7-8626-bb89b9614341
2025-09-19 15:17:31,332 - __main__ - INFO - 成功处理supplier: 100189
2025-09-19 15:17:31,332 - __main__ - INFO - 进度: 191/215
2025-09-19 15:17:31,332 - __main__ - INFO - 处理supplier: 100190
2025-09-19 15:17:31,333 - __main__ - INFO - 找到对应vendor: da3cbc5a-3035-4d2b-8e5a-de3056d8f8fc
2025-09-19 15:17:31,334 - database_manager - INFO - 成功删除vendor联系方式: da3cbc5a-3035-4d2b-8e5a-de3056d8f8fc
2025-09-19 15:17:31,334 - __main__ - INFO - 成功处理supplier: 100190
2025-09-19 15:17:31,334 - __main__ - INFO - 进度: 192/215
2025-09-19 15:17:31,334 - __main__ - INFO - 处理supplier: 100191
2025-09-19 15:17:31,335 - __main__ - INFO - 找到对应vendor: 025ea634-8a99-4b78-a47a-e1924a30ac53
2025-09-19 15:17:31,335 - database_manager - INFO - 成功更新vendor: 025ea634-8a99-4b78-a47a-e1924a30ac53
2025-09-19 15:17:31,335 - __main__ - INFO - 成功更新vendor: 025ea634-8a99-4b78-a47a-e1924a30ac53
2025-09-19 15:17:31,336 - database_manager - INFO - 成功删除vendor联系方式: 025ea634-8a99-4b78-a47a-e1924a30ac53
2025-09-19 15:17:31,337 - __main__ - INFO - 成功处理supplier: 100191
2025-09-19 15:17:31,337 - __main__ - INFO - 进度: 193/215
2025-09-19 15:17:31,338 - __main__ - INFO - 处理supplier: 100192
2025-09-19 15:17:31,338 - __main__ - INFO - 找到对应vendor: ab86a05e-dcb1-4cd3-a8c4-99f542a36cd8
2025-09-19 15:17:31,339 - database_manager - INFO - 成功更新vendor: ab86a05e-dcb1-4cd3-a8c4-99f542a36cd8
2025-09-19 15:17:31,339 - __main__ - INFO - 成功更新vendor: ab86a05e-dcb1-4cd3-a8c4-99f542a36cd8
2025-09-19 15:17:31,340 - database_manager - INFO - 成功删除vendor联系方式: ab86a05e-dcb1-4cd3-a8c4-99f542a36cd8
2025-09-19 15:17:31,342 - __main__ - INFO - 成功处理supplier: 100192
2025-09-19 15:17:31,342 - __main__ - INFO - 进度: 194/215
2025-09-19 15:17:31,342 - __main__ - INFO - 处理supplier: 100193
2025-09-19 15:17:31,343 - __main__ - INFO - 找到对应vendor: 4a35b4ce-4c07-4aff-b992-33c5eca975a9
2025-09-19 15:17:31,344 - database_manager - INFO - 成功删除vendor联系方式: 4a35b4ce-4c07-4aff-b992-33c5eca975a9
2025-09-19 15:17:31,345 - __main__ - INFO - 成功处理supplier: 100193
2025-09-19 15:17:31,345 - __main__ - INFO - 进度: 195/215
2025-09-19 15:17:31,345 - __main__ - INFO - 处理supplier: 100194
2025-09-19 15:17:31,346 - __main__ - INFO - 找到对应vendor: a9a6ae9d-f24e-4997-ac5e-2c5f06f623f2
2025-09-19 15:17:31,346 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:31,346 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,346 - database_manager - INFO - 成功更新vendor: a9a6ae9d-f24e-4997-ac5e-2c5f06f623f2
2025-09-19 15:17:31,347 - __main__ - INFO - 成功更新vendor: a9a6ae9d-f24e-4997-ac5e-2c5f06f623f2
2025-09-19 15:17:31,348 - database_manager - INFO - 成功删除vendor联系方式: a9a6ae9d-f24e-4997-ac5e-2c5f06f623f2
2025-09-19 15:17:31,348 - __main__ - INFO - 成功处理supplier: 100194
2025-09-19 15:17:31,348 - __main__ - INFO - 进度: 196/215
2025-09-19 15:17:31,348 - __main__ - INFO - 处理supplier: 100195
2025-09-19 15:17:31,349 - __main__ - WARNING - 未找到对应的vendor，跳过: 100195
2025-09-19 15:17:31,349 - __main__ - INFO - 进度: 197/215
2025-09-19 15:17:31,349 - __main__ - INFO - 处理supplier: 100196
2025-09-19 15:17:31,349 - __main__ - WARNING - 未找到对应的vendor，跳过: 100196
2025-09-19 15:17:31,349 - __main__ - INFO - 进度: 198/215
2025-09-19 15:17:31,349 - __main__ - INFO - 处理supplier: 100197
2025-09-19 15:17:31,350 - __main__ - INFO - 找到对应vendor: fcb471c3-95f3-4813-a719-8055482986c1
2025-09-19 15:17:31,350 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,350 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:31,350 - database_manager - INFO - 成功更新vendor: fcb471c3-95f3-4813-a719-8055482986c1
2025-09-19 15:17:31,351 - __main__ - INFO - 成功更新vendor: fcb471c3-95f3-4813-a719-8055482986c1
2025-09-19 15:17:31,352 - database_manager - INFO - 成功删除vendor联系方式: fcb471c3-95f3-4813-a719-8055482986c1
2025-09-19 15:17:31,354 - __main__ - INFO - 成功处理supplier: 100197
2025-09-19 15:17:31,354 - __main__ - INFO - 进度: 199/215
2025-09-19 15:17:31,354 - __main__ - INFO - 处理supplier: 100198
2025-09-19 15:17:31,355 - __main__ - INFO - 找到对应vendor: 993aa272-f026-493e-a914-8058b984048e
2025-09-19 15:17:31,355 - database_manager - INFO - 成功更新vendor: 993aa272-f026-493e-a914-8058b984048e
2025-09-19 15:17:31,356 - __main__ - INFO - 成功更新vendor: 993aa272-f026-493e-a914-8058b984048e
2025-09-19 15:17:31,357 - database_manager - INFO - 成功删除vendor联系方式: 993aa272-f026-493e-a914-8058b984048e
2025-09-19 15:17:31,358 - __main__ - INFO - 成功处理supplier: 100198
2025-09-19 15:17:31,358 - __main__ - INFO - 进度: 200/215
2025-09-19 15:17:31,358 - __main__ - INFO - 处理supplier: 100199
2025-09-19 15:17:31,359 - __main__ - INFO - 找到对应vendor: 33c4b05d-c874-4833-a063-7284ff28bc00
2025-09-19 15:17:31,359 - database_manager - INFO - 成功更新vendor: 33c4b05d-c874-4833-a063-7284ff28bc00
2025-09-19 15:17:31,360 - __main__ - INFO - 成功更新vendor: 33c4b05d-c874-4833-a063-7284ff28bc00
2025-09-19 15:17:31,361 - database_manager - INFO - 成功删除vendor联系方式: 33c4b05d-c874-4833-a063-7284ff28bc00
2025-09-19 15:17:31,362 - __main__ - INFO - 成功处理supplier: 100199
2025-09-19 15:17:31,362 - __main__ - INFO - 进度: 201/215
2025-09-19 15:17:31,362 - __main__ - INFO - 处理supplier: 100200
2025-09-19 15:17:31,363 - __main__ - INFO - 找到对应vendor: 22b61b5d-4819-4be6-b4bf-7c52813c7670
2025-09-19 15:17:31,364 - database_manager - INFO - 成功删除vendor联系方式: 22b61b5d-4819-4be6-b4bf-7c52813c7670
2025-09-19 15:17:31,365 - __main__ - INFO - 成功处理supplier: 100200
2025-09-19 15:17:31,365 - __main__ - INFO - 进度: 202/215
2025-09-19 15:17:31,365 - __main__ - INFO - 处理supplier: 100201
2025-09-19 15:17:31,365 - __main__ - INFO - 找到对应vendor: 492eda25-4b9a-4a01-8497-a62ba42a05fa
2025-09-19 15:17:31,365 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:31,366 - database_manager - INFO - 成功更新vendor: 492eda25-4b9a-4a01-8497-a62ba42a05fa
2025-09-19 15:17:31,366 - __main__ - INFO - 成功更新vendor: 492eda25-4b9a-4a01-8497-a62ba42a05fa
2025-09-19 15:17:31,367 - database_manager - INFO - 成功删除vendor联系方式: 492eda25-4b9a-4a01-8497-a62ba42a05fa
2025-09-19 15:17:31,369 - __main__ - INFO - 成功处理supplier: 100201
2025-09-19 15:17:31,369 - __main__ - INFO - 进度: 203/215
2025-09-19 15:17:31,369 - __main__ - INFO - 处理supplier: 100202
2025-09-19 15:17:31,369 - __main__ - INFO - 找到对应vendor: de26f7ec-0eaa-4071-be8b-093ac89ae959
2025-09-19 15:17:31,370 - database_manager - INFO - 成功删除vendor联系方式: de26f7ec-0eaa-4071-be8b-093ac89ae959
2025-09-19 15:17:31,371 - __main__ - INFO - 成功处理supplier: 100202
2025-09-19 15:17:31,371 - __main__ - INFO - 进度: 204/215
2025-09-19 15:17:31,371 - __main__ - INFO - 处理supplier: 100203
2025-09-19 15:17:31,372 - __main__ - INFO - 找到对应vendor: c569b030-606d-4abb-af4d-57b8ef225518
2025-09-19 15:17:31,372 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 15:17:31,372 - data_mapper - WARNING - 电话号码为空，跳过
2025-09-19 15:17:31,372 - database_manager - INFO - 成功更新vendor: c569b030-606d-4abb-af4d-57b8ef225518
2025-09-19 15:17:31,373 - __main__ - INFO - 成功更新vendor: c569b030-606d-4abb-af4d-57b8ef225518
2025-09-19 15:17:31,374 - database_manager - INFO - 成功删除vendor联系方式: c569b030-606d-4abb-af4d-57b8ef225518
2025-09-19 15:17:31,376 - __main__ - INFO - 成功处理supplier: 100203
2025-09-19 15:17:31,376 - __main__ - INFO - 进度: 205/215
2025-09-19 15:17:31,376 - __main__ - INFO - 处理supplier: 100204
2025-09-19 15:17:31,377 - __main__ - INFO - 找到对应vendor: d23836f6-c835-44be-bf18-2f47ed6fafd1
2025-09-19 15:17:31,377 - database_manager - INFO - 成功更新vendor: d23836f6-c835-44be-bf18-2f47ed6fafd1
2025-09-19 15:17:31,378 - __main__ - INFO - 成功更新vendor: d23836f6-c835-44be-bf18-2f47ed6fafd1
2025-09-19 15:17:31,379 - database_manager - INFO - 成功删除vendor联系方式: d23836f6-c835-44be-bf18-2f47ed6fafd1
2025-09-19 15:17:31,380 - __main__ - INFO - 成功处理supplier: 100204
2025-09-19 15:17:31,380 - __main__ - INFO - 进度: 206/215
2025-09-19 15:17:31,380 - __main__ - INFO - 处理supplier: 100205
2025-09-19 15:17:31,381 - __main__ - INFO - 找到对应vendor: d76624b2-8cb8-4cb1-a93b-947684f4713f
2025-09-19 15:17:31,381 - database_manager - INFO - 成功更新vendor: d76624b2-8cb8-4cb1-a93b-947684f4713f
2025-09-19 15:17:31,381 - __main__ - INFO - 成功更新vendor: d76624b2-8cb8-4cb1-a93b-947684f4713f
2025-09-19 15:17:31,383 - database_manager - INFO - 成功删除vendor联系方式: d76624b2-8cb8-4cb1-a93b-947684f4713f
2025-09-19 15:17:31,383 - __main__ - INFO - 成功处理supplier: 100205
2025-09-19 15:17:31,383 - __main__ - INFO - 进度: 207/215
2025-09-19 15:17:31,383 - __main__ - INFO - 处理supplier: 100206
2025-09-19 15:17:31,384 - __main__ - INFO - 找到对应vendor: ca65261b-02a7-48bc-b85e-c0c9a4861ac7
2025-09-19 15:17:31,384 - database_manager - INFO - 成功更新vendor: ca65261b-02a7-48bc-b85e-c0c9a4861ac7
2025-09-19 15:17:31,384 - __main__ - INFO - 成功更新vendor: ca65261b-02a7-48bc-b85e-c0c9a4861ac7
2025-09-19 15:17:31,385 - database_manager - INFO - 成功删除vendor联系方式: ca65261b-02a7-48bc-b85e-c0c9a4861ac7
2025-09-19 15:17:31,387 - __main__ - INFO - 成功处理supplier: 100206
2025-09-19 15:17:31,387 - __main__ - INFO - 进度: 208/215
2025-09-19 15:17:31,387 - __main__ - INFO - 处理supplier: 100207
2025-09-19 15:17:31,388 - __main__ - INFO - 找到对应vendor: 86adf4e5-0e2a-4039-b728-cf8eef682cda
2025-09-19 15:17:31,388 - database_manager - INFO - 成功更新vendor: 86adf4e5-0e2a-4039-b728-cf8eef682cda
2025-09-19 15:17:31,389 - __main__ - INFO - 成功更新vendor: 86adf4e5-0e2a-4039-b728-cf8eef682cda
2025-09-19 15:17:31,390 - database_manager - INFO - 成功删除vendor联系方式: 86adf4e5-0e2a-4039-b728-cf8eef682cda
2025-09-19 15:17:31,392 - __main__ - INFO - 成功处理supplier: 100207
2025-09-19 15:17:31,392 - __main__ - INFO - 进度: 209/215
2025-09-19 15:17:31,392 - __main__ - INFO - 处理supplier: 100208
2025-09-19 15:17:31,393 - __main__ - WARNING - 未找到对应的vendor，跳过: 100208
2025-09-19 15:17:31,393 - __main__ - INFO - 进度: 210/215
2025-09-19 15:17:31,393 - __main__ - INFO - 处理supplier: 100209
2025-09-19 15:17:31,393 - __main__ - WARNING - 未找到对应的vendor，跳过: 100209
2025-09-19 15:17:31,394 - __main__ - INFO - 进度: 211/215
2025-09-19 15:17:31,394 - __main__ - INFO - 处理supplier: 100210
2025-09-19 15:17:31,394 - __main__ - WARNING - 未找到对应的vendor，跳过: 100210
2025-09-19 15:17:31,394 - __main__ - INFO - 进度: 212/215
2025-09-19 15:17:31,394 - __main__ - INFO - 处理supplier: 100211
2025-09-19 15:17:31,395 - __main__ - WARNING - 未找到对应的vendor，跳过: 100211
2025-09-19 15:17:31,395 - __main__ - INFO - 进度: 213/215
2025-09-19 15:17:31,395 - __main__ - INFO - 处理supplier: 100212
2025-09-19 15:17:31,395 - __main__ - WARNING - 未找到对应的vendor，跳过: 100212
2025-09-19 15:17:31,395 - __main__ - INFO - 进度: 214/215
2025-09-19 15:17:31,395 - __main__ - INFO - 处理supplier: 100213
2025-09-19 15:17:31,396 - __main__ - INFO - 找到对应vendor: 5a5a4506-3856-4547-87e7-8f88493add4c
2025-09-19 15:17:31,397 - database_manager - INFO - 成功删除vendor联系方式: 5a5a4506-3856-4547-87e7-8f88493add4c
2025-09-19 15:17:31,398 - __main__ - INFO - 成功处理supplier: 100213
2025-09-19 15:17:31,398 - __main__ - INFO - 进度: 215/215
2025-09-19 15:17:31,398 - __main__ - INFO - 处理supplier: PRIMARY
2025-09-19 15:17:31,398 - __main__ - WARNING - 未找到对应的vendor，跳过: PRIMARY
2025-09-19 15:17:31,399 - __main__ - INFO - ==================================================
2025-09-19 15:17:31,399 - __main__ - INFO - 迁移统计信息:
2025-09-19 15:17:31,399 - __main__ - INFO - 总supplier数量: 215
2025-09-19 15:17:31,399 - __main__ - INFO - 已处理supplier: 116
2025-09-19 15:17:31,399 - __main__ - INFO - 跳过的supplier: 99
2025-09-19 15:17:31,399 - __main__ - INFO - 更新的vendor: 101
2025-09-19 15:17:31,399 - __main__ - INFO - 插入的地址: 0
2025-09-19 15:17:31,399 - __main__ - INFO - 插入的邮箱: 149
2025-09-19 15:17:31,399 - __main__ - INFO - 插入的电话: 0
2025-09-19 15:17:31,399 - __main__ - INFO - 插入的网址: 15
2025-09-19 15:17:31,399 - __main__ - INFO - 插入的额外信息: 0
2025-09-19 15:17:31,399 - __main__ - INFO - 错误数量: 0
2025-09-19 15:17:31,399 - __main__ - INFO - ==================================================
2025-09-19 15:17:31,399 - __main__ - INFO - 迁移完成，耗时: 0:01:25.741585
2025-09-19 16:15:17,180 - __main__ - INFO - 日志系统初始化完成
2025-09-19 16:15:17,180 - __main__ - INFO - 开始Finale Supplier数据迁移
2025-09-19 16:15:17,180 - __main__ - INFO - 验证连接...
2025-09-19 16:15:18,943 - __main__ - INFO - 所有连接验证成功
2025-09-19 16:15:18,943 - __main__ - INFO - 开始获取Finale supplier数据...
2025-09-19 16:15:18,943 - finale_api_client - INFO - 正在获取partygroup列表: https://app.finaleinventory.com/mercaso/api/partygroup
2025-09-19 16:15:19,736 - finale_api_client - INFO - 成功获取partygroup列表，共 215 个supplier
2025-09-19 16:15:19,736 - finale_api_client - INFO - 正在处理supplier 1/215: 100000
2025-09-19 16:15:20,061 - finale_api_client - INFO - 正在处理supplier 2/215: 100001
2025-09-19 16:15:20,428 - finale_api_client - INFO - 正在处理supplier 3/215: 100002
2025-09-19 16:15:20,742 - finale_api_client - INFO - 正在处理supplier 4/215: 100003
2025-09-19 16:15:21,073 - finale_api_client - INFO - 正在处理supplier 5/215: 100004
2025-09-19 16:15:21,433 - finale_api_client - INFO - 正在处理supplier 6/215: 100005
2025-09-19 16:15:21,780 - finale_api_client - INFO - 正在处理supplier 7/215: 100006
2025-09-19 16:15:22,105 - finale_api_client - INFO - 正在处理supplier 8/215: 100007
2025-09-19 16:15:22,955 - finale_api_client - INFO - 正在处理supplier 9/215: 100008
2025-09-19 16:15:23,317 - finale_api_client - INFO - 正在处理supplier 10/215: 100009
2025-09-19 16:15:23,674 - finale_api_client - INFO - 正在处理supplier 11/215: 100010
2025-09-19 16:15:24,044 - finale_api_client - INFO - 正在处理supplier 12/215: 100011
2025-09-19 16:15:24,388 - finale_api_client - INFO - 正在处理supplier 13/215: 100012
2025-09-19 16:15:25,011 - finale_api_client - INFO - 正在处理supplier 14/215: 100013
2025-09-19 16:15:25,491 - finale_api_client - INFO - 正在处理supplier 15/215: 100014
2025-09-19 16:15:25,823 - finale_api_client - INFO - 正在处理supplier 16/215: 100015
2025-09-19 16:15:26,234 - finale_api_client - INFO - 正在处理supplier 17/215: 100016
2025-09-19 16:15:26,567 - finale_api_client - INFO - 正在处理supplier 18/215: 100017
2025-09-19 16:15:26,904 - finale_api_client - INFO - 正在处理supplier 19/215: 100018
2025-09-19 16:15:27,252 - finale_api_client - INFO - 正在处理supplier 20/215: 100019
2025-09-19 16:15:27,612 - finale_api_client - INFO - 正在处理supplier 21/215: 100020
2025-09-19 16:15:27,963 - finale_api_client - INFO - 正在处理supplier 22/215: 100021
2025-09-19 16:15:28,351 - finale_api_client - INFO - 正在处理supplier 23/215: 100022
2025-09-19 16:15:28,739 - finale_api_client - INFO - 正在处理supplier 24/215: 100023
2025-09-19 16:15:29,066 - finale_api_client - INFO - 正在处理supplier 25/215: 100024
2025-09-19 16:15:29,386 - finale_api_client - INFO - 正在处理supplier 26/215: 100025
2025-09-19 16:15:29,730 - finale_api_client - INFO - 正在处理supplier 27/215: 100026
2025-09-19 16:15:30,072 - finale_api_client - INFO - 正在处理supplier 28/215: 100027
2025-09-19 16:15:30,408 - finale_api_client - INFO - 正在处理supplier 29/215: 100028
2025-09-19 16:15:30,759 - finale_api_client - INFO - 正在处理supplier 30/215: 100029
2025-09-19 16:15:31,093 - finale_api_client - INFO - 正在处理supplier 31/215: 100030
2025-09-19 16:15:31,433 - finale_api_client - INFO - 正在处理supplier 32/215: 100031
2025-09-19 16:15:32,091 - finale_api_client - INFO - 正在处理supplier 33/215: 100032
2025-09-19 16:15:32,418 - finale_api_client - INFO - 正在处理supplier 34/215: 100033
2025-09-19 16:15:32,726 - finale_api_client - INFO - 正在处理supplier 35/215: 100034
2025-09-19 16:15:33,346 - finale_api_client - INFO - 正在处理supplier 36/215: 100035
2025-09-19 16:15:33,701 - finale_api_client - INFO - 正在处理supplier 37/215: 100036
2025-09-19 16:15:34,024 - finale_api_client - INFO - 正在处理supplier 38/215: 100037
2025-09-19 16:15:34,441 - finale_api_client - INFO - 正在处理supplier 39/215: 100038
2025-09-19 16:15:34,785 - finale_api_client - INFO - 正在处理supplier 40/215: 100039
2025-09-19 16:15:35,145 - finale_api_client - INFO - 正在处理supplier 41/215: 100040
2025-09-19 16:15:35,558 - finale_api_client - INFO - 正在处理supplier 42/215: 100041
2025-09-19 16:15:36,275 - finale_api_client - INFO - 正在处理supplier 43/215: 100042
2025-09-19 16:15:36,600 - finale_api_client - INFO - 正在处理supplier 44/215: 100043
2025-09-19 16:15:36,936 - finale_api_client - INFO - 正在处理supplier 45/215: 100044
2025-09-19 16:15:37,279 - finale_api_client - INFO - 正在处理supplier 46/215: 100045
2025-09-19 16:15:37,860 - finale_api_client - INFO - 正在处理supplier 47/215: 100046
2025-09-19 16:15:38,215 - finale_api_client - INFO - 正在处理supplier 48/215: 100047
2025-09-19 16:15:38,601 - finale_api_client - INFO - 正在处理supplier 49/215: 100048
2025-09-19 16:15:39,043 - finale_api_client - INFO - 正在处理supplier 50/215: 100049
2025-09-19 16:15:39,354 - finale_api_client - INFO - 正在处理supplier 51/215: 100050
2025-09-19 16:15:39,689 - finale_api_client - INFO - 正在处理supplier 52/215: 100051
2025-09-19 16:15:40,003 - finale_api_client - INFO - 正在处理supplier 53/215: 100052
2025-09-19 16:15:40,347 - finale_api_client - INFO - 正在处理supplier 54/215: 100053
2025-09-19 16:15:40,683 - finale_api_client - INFO - 正在处理supplier 55/215: 100054
2025-09-19 16:15:41,094 - finale_api_client - INFO - 正在处理supplier 56/215: 100055
2025-09-19 16:15:41,555 - finale_api_client - INFO - 正在处理supplier 57/215: 100056
2025-09-19 16:15:41,949 - finale_api_client - INFO - 正在处理supplier 58/215: 100057
2025-09-19 16:15:42,376 - finale_api_client - INFO - 正在处理supplier 59/215: 100058
2025-09-19 16:15:42,732 - finale_api_client - INFO - 正在处理supplier 60/215: 100059
2025-09-19 16:15:43,141 - finale_api_client - INFO - 正在处理supplier 61/215: 100060
2025-09-19 16:15:43,563 - finale_api_client - INFO - 正在处理supplier 62/215: 100061
2025-09-19 16:15:43,880 - finale_api_client - INFO - 正在处理supplier 63/215: 100062
2025-09-19 16:15:44,220 - finale_api_client - INFO - 正在处理supplier 64/215: 100063
2025-09-19 16:15:44,565 - finale_api_client - INFO - 正在处理supplier 65/215: 100064
2025-09-19 16:15:44,970 - finale_api_client - INFO - 正在处理supplier 66/215: 100065
2025-09-19 16:15:45,387 - finale_api_client - INFO - 正在处理supplier 67/215: 100066
2025-09-19 16:15:45,740 - finale_api_client - INFO - 正在处理supplier 68/215: 100067
2025-09-19 16:15:46,110 - finale_api_client - INFO - 正在处理supplier 69/215: 100068
2025-09-19 16:15:46,439 - finale_api_client - INFO - 正在处理supplier 70/215: 100069
2025-09-19 16:15:46,769 - finale_api_client - INFO - 正在处理supplier 71/215: 100070
2025-09-19 16:15:47,366 - finale_api_client - INFO - 正在处理supplier 72/215: 100071
2025-09-19 16:15:47,739 - finale_api_client - INFO - 正在处理supplier 73/215: 100072
2025-09-19 16:15:48,252 - finale_api_client - INFO - 正在处理supplier 74/215: 100073
2025-09-19 16:15:48,613 - finale_api_client - INFO - 正在处理supplier 75/215: 100074
2025-09-19 16:15:49,227 - finale_api_client - INFO - 正在处理supplier 76/215: 100075
2025-09-19 16:15:49,561 - finale_api_client - INFO - 正在处理supplier 77/215: 100076
2025-09-19 16:15:49,898 - finale_api_client - INFO - 正在处理supplier 78/215: 100077
2025-09-19 16:15:50,235 - finale_api_client - INFO - 正在处理supplier 79/215: 100078
2025-09-19 16:15:50,605 - finale_api_client - INFO - 正在处理supplier 80/215: 100079
2025-09-19 16:15:50,970 - finale_api_client - INFO - 正在处理supplier 81/215: 100080
2025-09-19 16:15:51,332 - finale_api_client - INFO - 正在处理supplier 82/215: 100081
2025-09-19 16:15:51,733 - finale_api_client - INFO - 正在处理supplier 83/215: 100082
2025-09-19 16:15:52,090 - finale_api_client - INFO - 正在处理supplier 84/215: 100083
2025-09-19 16:15:52,421 - finale_api_client - INFO - 正在处理supplier 85/215: 100084
2025-09-19 16:15:52,779 - finale_api_client - INFO - 正在处理supplier 86/215: 100085
2025-09-19 16:15:53,270 - finale_api_client - INFO - 正在处理supplier 87/215: 100086
2025-09-19 16:15:53,582 - finale_api_client - INFO - 正在处理supplier 88/215: 100087
2025-09-19 16:15:53,985 - finale_api_client - INFO - 正在处理supplier 89/215: 100088
2025-09-19 16:15:54,395 - finale_api_client - INFO - 正在处理supplier 90/215: 100089
2025-09-19 16:15:54,751 - finale_api_client - INFO - 正在处理supplier 91/215: 100090
2025-09-19 16:15:55,076 - finale_api_client - INFO - 正在处理supplier 92/215: 100091
2025-09-19 16:15:55,721 - finale_api_client - INFO - 正在处理supplier 93/215: 100092
2025-09-19 16:15:56,064 - finale_api_client - INFO - 正在处理supplier 94/215: 100093
2025-09-19 16:15:56,448 - finale_api_client - INFO - 正在处理supplier 95/215: 100094
2025-09-19 16:15:56,783 - finale_api_client - INFO - 正在处理supplier 96/215: 100095
2025-09-19 16:15:57,178 - finale_api_client - INFO - 正在处理supplier 97/215: 100096
2025-09-19 16:15:57,594 - finale_api_client - INFO - 正在处理supplier 98/215: 100097
2025-09-19 16:15:57,934 - finale_api_client - INFO - 正在处理supplier 99/215: 100098
2025-09-19 16:15:59,360 - finale_api_client - INFO - 正在处理supplier 100/215: 100099
2025-09-19 16:15:59,695 - finale_api_client - INFO - 正在处理supplier 101/215: 100100
2025-09-19 16:16:00,059 - finale_api_client - INFO - 正在处理supplier 102/215: 100101
2025-09-19 16:16:00,388 - finale_api_client - INFO - 正在处理supplier 103/215: 100102
2025-09-19 16:16:00,749 - finale_api_client - INFO - 正在处理supplier 104/215: 100103
2025-09-19 16:16:01,066 - finale_api_client - INFO - 正在处理supplier 105/215: 100104
2025-09-19 16:16:02,077 - finale_api_client - INFO - 正在处理supplier 106/215: 100105
2025-09-19 16:16:02,690 - finale_api_client - INFO - 正在处理supplier 107/215: 100106
2025-09-19 16:16:03,025 - finale_api_client - INFO - 正在处理supplier 108/215: 100107
2025-09-19 16:16:03,406 - finale_api_client - INFO - 正在处理supplier 109/215: 100108
2025-09-19 16:16:04,064 - finale_api_client - INFO - 正在处理supplier 110/215: 100109
2025-09-19 16:16:04,689 - finale_api_client - INFO - 正在处理supplier 111/215: 100110
2025-09-19 16:16:05,020 - finale_api_client - INFO - 正在处理supplier 112/215: 100111
2025-09-19 16:16:05,454 - finale_api_client - INFO - 正在处理supplier 113/215: 100112
2025-09-19 16:16:06,085 - finale_api_client - INFO - 正在处理supplier 114/215: 100113
2025-09-19 16:16:06,420 - finale_api_client - INFO - 正在处理supplier 115/215: 100114
2025-09-19 16:16:06,769 - finale_api_client - INFO - 正在处理supplier 116/215: 100115
2025-09-19 16:16:07,091 - finale_api_client - INFO - 正在处理supplier 117/215: 100116
2025-09-19 16:16:07,429 - finale_api_client - INFO - 正在处理supplier 118/215: 100117
2025-09-19 16:16:08,037 - finale_api_client - INFO - 正在处理supplier 119/215: 100118
2025-09-19 16:16:08,347 - finale_api_client - INFO - 正在处理supplier 120/215: 100119
2025-09-19 16:16:08,752 - finale_api_client - INFO - 正在处理supplier 121/215: 100120
2025-09-19 16:16:09,152 - finale_api_client - INFO - 正在处理supplier 122/215: 100121
2025-09-19 16:16:09,542 - finale_api_client - INFO - 正在处理supplier 123/215: 100122
2025-09-19 16:16:09,884 - finale_api_client - INFO - 正在处理supplier 124/215: 100123
2025-09-19 16:16:10,252 - finale_api_client - INFO - 正在处理supplier 125/215: 100124
2025-09-19 16:16:10,652 - finale_api_client - INFO - 正在处理supplier 126/215: 100125
2025-09-19 16:16:11,060 - finale_api_client - INFO - 正在处理supplier 127/215: 100126
2025-09-19 16:16:11,399 - finale_api_client - INFO - 正在处理supplier 128/215: 100127
2025-09-19 16:16:11,802 - finale_api_client - INFO - 正在处理supplier 129/215: 100128
2025-09-19 16:16:12,129 - finale_api_client - INFO - 正在处理supplier 130/215: 100129
2025-09-19 16:16:12,522 - finale_api_client - INFO - 正在处理supplier 131/215: 100130
2025-09-19 16:16:12,871 - finale_api_client - INFO - 正在处理supplier 132/215: 100131
2025-09-19 16:16:13,242 - finale_api_client - INFO - 正在处理supplier 133/215: 100132
2025-09-19 16:16:13,572 - finale_api_client - INFO - 正在处理supplier 134/215: 100133
2025-09-19 16:16:13,906 - finale_api_client - INFO - 正在处理supplier 135/215: 100134
2025-09-19 16:16:14,282 - finale_api_client - INFO - 正在处理supplier 136/215: 100135
2025-09-19 16:16:14,609 - finale_api_client - INFO - 正在处理supplier 137/215: 100136
2025-09-19 16:16:14,953 - finale_api_client - INFO - 正在处理supplier 138/215: 100137
2025-09-19 16:16:15,347 - finale_api_client - INFO - 正在处理supplier 139/215: 100138
2025-09-19 16:16:15,698 - finale_api_client - INFO - 正在处理supplier 140/215: 100139
2025-09-19 16:16:16,018 - finale_api_client - INFO - 正在处理supplier 141/215: 100140
2025-09-19 16:16:16,619 - finale_api_client - INFO - 正在处理supplier 142/215: 100141
2025-09-19 16:16:17,028 - finale_api_client - INFO - 正在处理supplier 143/215: 100142
2025-09-19 16:16:17,357 - finale_api_client - INFO - 正在处理supplier 144/215: 100143
2025-09-19 16:16:17,683 - finale_api_client - INFO - 正在处理supplier 145/215: 100144
2025-09-19 16:16:18,556 - finale_api_client - INFO - 正在处理supplier 146/215: 100145
2025-09-19 16:16:18,908 - finale_api_client - INFO - 正在处理supplier 147/215: 100146
2025-09-19 16:16:19,246 - finale_api_client - INFO - 正在处理supplier 148/215: 100147
2025-09-19 16:16:19,591 - finale_api_client - INFO - 正在处理supplier 149/215: 100148
2025-09-19 16:16:19,940 - finale_api_client - INFO - 正在处理supplier 150/215: 100149
2025-09-19 16:16:20,509 - finale_api_client - INFO - 正在处理supplier 151/215: 100150
2025-09-19 16:16:21,020 - finale_api_client - INFO - 正在处理supplier 152/215: 100151
2025-09-19 16:16:21,348 - finale_api_client - INFO - 正在处理supplier 153/215: 100152
2025-09-19 16:16:21,737 - finale_api_client - INFO - 正在处理supplier 154/215: 100153
2025-09-19 16:16:22,090 - finale_api_client - INFO - 正在处理supplier 155/215: 100154
2025-09-19 16:16:22,759 - finale_api_client - INFO - 正在处理supplier 156/215: 100155
2025-09-19 16:16:23,136 - finale_api_client - INFO - 正在处理supplier 157/215: 100156
2025-09-19 16:16:23,500 - finale_api_client - INFO - 正在处理supplier 158/215: 100157
2025-09-19 16:16:23,869 - finale_api_client - INFO - 正在处理supplier 159/215: 100158
2025-09-19 16:16:24,192 - finale_api_client - INFO - 正在处理supplier 160/215: 100159
2025-09-19 16:16:24,517 - finale_api_client - INFO - 正在处理supplier 161/215: 100160
2025-09-19 16:16:24,839 - finale_api_client - INFO - 正在处理supplier 162/215: 100161
2025-09-19 16:16:25,218 - finale_api_client - INFO - 正在处理supplier 163/215: 100162
2025-09-19 16:16:25,566 - finale_api_client - INFO - 正在处理supplier 164/215: 100163
2025-09-19 16:16:25,938 - finale_api_client - INFO - 正在处理supplier 165/215: 100164
2025-09-19 16:16:26,551 - finale_api_client - INFO - 正在处理supplier 166/215: 100165
2025-09-19 16:16:27,711 - finale_api_client - INFO - 正在处理supplier 167/215: 100166
2025-09-19 16:16:28,037 - finale_api_client - INFO - 正在处理supplier 168/215: 100167
2025-09-19 16:16:28,355 - finale_api_client - INFO - 正在处理supplier 169/215: 100168
2025-09-19 16:16:28,688 - finale_api_client - INFO - 正在处理supplier 170/215: 100169
2025-09-19 16:16:29,112 - finale_api_client - INFO - 正在处理supplier 171/215: 100170
2025-09-19 16:16:29,446 - finale_api_client - INFO - 正在处理supplier 172/215: 100171
2025-09-19 16:16:29,802 - finale_api_client - INFO - 正在处理supplier 173/215: 100172
2025-09-19 16:16:30,117 - finale_api_client - INFO - 正在处理supplier 174/215: 100173
2025-09-19 16:16:30,451 - finale_api_client - INFO - 正在处理supplier 175/215: 100174
2025-09-19 16:16:30,783 - finale_api_client - INFO - 正在处理supplier 176/215: 100175
2025-09-19 16:16:31,092 - finale_api_client - INFO - 正在处理supplier 177/215: 100176
2025-09-19 16:16:31,436 - finale_api_client - INFO - 正在处理supplier 178/215: 100177
2025-09-19 16:16:31,765 - finale_api_client - INFO - 正在处理supplier 179/215: 100178
2025-09-19 16:16:32,103 - finale_api_client - INFO - 正在处理supplier 180/215: 100179
2025-09-19 16:16:32,445 - finale_api_client - INFO - 正在处理supplier 181/215: 100180
2025-09-19 16:16:32,809 - finale_api_client - INFO - 正在处理supplier 182/215: 100181
2025-09-19 16:16:33,149 - finale_api_client - INFO - 正在处理supplier 183/215: 100182
2025-09-19 16:16:33,477 - finale_api_client - INFO - 正在处理supplier 184/215: 100183
2025-09-19 16:16:33,830 - finale_api_client - INFO - 正在处理supplier 185/215: 100184
2025-09-19 16:16:34,168 - finale_api_client - INFO - 正在处理supplier 186/215: 100185
2025-09-19 16:16:34,492 - finale_api_client - INFO - 正在处理supplier 187/215: 100186
2025-09-19 16:16:34,812 - finale_api_client - INFO - 正在处理supplier 188/215: 100187
2025-09-19 16:16:35,151 - finale_api_client - INFO - 正在处理supplier 189/215: 100188
2025-09-19 16:16:35,568 - finale_api_client - INFO - 正在处理supplier 190/215: 100189
2025-09-19 16:16:35,882 - finale_api_client - INFO - 正在处理supplier 191/215: 100190
2025-09-19 16:16:36,251 - finale_api_client - INFO - 正在处理supplier 192/215: 100191
2025-09-19 16:16:36,570 - finale_api_client - INFO - 正在处理supplier 193/215: 100192
2025-09-19 16:16:37,190 - finale_api_client - INFO - 正在处理supplier 194/215: 100193
2025-09-19 16:16:37,752 - finale_api_client - INFO - 正在处理supplier 195/215: 100194
2025-09-19 16:16:38,123 - finale_api_client - INFO - 正在处理supplier 196/215: 100195
2025-09-19 16:16:38,535 - finale_api_client - INFO - 正在处理supplier 197/215: 100196
2025-09-19 16:16:38,859 - finale_api_client - INFO - 正在处理supplier 198/215: 100197
2025-09-19 16:16:39,242 - finale_api_client - INFO - 正在处理supplier 199/215: 100198
2025-09-19 16:16:41,118 - finale_api_client - INFO - 正在处理supplier 200/215: 100199
2025-09-19 16:16:41,446 - finale_api_client - INFO - 正在处理supplier 201/215: 100200
2025-09-19 16:16:41,815 - finale_api_client - INFO - 正在处理supplier 202/215: 100201
2025-09-19 16:16:42,188 - finale_api_client - INFO - 正在处理supplier 203/215: 100202
2025-09-19 16:16:42,517 - finale_api_client - INFO - 正在处理supplier 204/215: 100203
2025-09-19 16:16:42,842 - finale_api_client - INFO - 正在处理supplier 205/215: 100204
2025-09-19 16:16:43,448 - finale_api_client - INFO - 正在处理supplier 206/215: 100205
2025-09-19 16:16:43,839 - finale_api_client - INFO - 正在处理supplier 207/215: 100206
2025-09-19 16:16:44,172 - finale_api_client - INFO - 正在处理supplier 208/215: 100207
2025-09-19 16:16:44,555 - finale_api_client - INFO - 正在处理supplier 209/215: 100208
2025-09-19 16:16:44,894 - finale_api_client - INFO - 正在处理supplier 210/215: 100209
2025-09-19 16:16:45,249 - finale_api_client - INFO - 正在处理supplier 211/215: 100210
2025-09-19 16:16:45,555 - finale_api_client - INFO - 正在处理supplier 212/215: 100211
2025-09-19 16:16:45,888 - finale_api_client - INFO - 正在处理supplier 213/215: 100212
2025-09-19 16:16:46,302 - finale_api_client - INFO - 正在处理supplier 214/215: 100213
2025-09-19 16:16:46,682 - finale_api_client - INFO - 正在处理supplier 215/215: PRIMARY
2025-09-19 16:16:47,290 - finale_api_client - INFO - 成功获取 215 个supplier的完整数据
2025-09-19 16:16:47,291 - __main__ - INFO - 成功获取 215 个supplier数据
2025-09-19 16:16:47,291 - __main__ - INFO - 开始处理 215 个supplier...
2025-09-19 16:16:47,291 - __main__ - INFO - 进度: 1/215
2025-09-19 16:16:47,291 - __main__ - INFO - 处理supplier: 100000
2025-09-19 16:16:47,307 - __main__ - WARNING - 未找到对应的vendor，跳过: 100000
2025-09-19 16:16:47,307 - __main__ - INFO - 进度: 2/215
2025-09-19 16:16:47,308 - __main__ - INFO - 处理supplier: 100001
2025-09-19 16:16:47,310 - __main__ - WARNING - 未找到对应的vendor，跳过: 100001
2025-09-19 16:16:47,310 - __main__ - INFO - 进度: 3/215
2025-09-19 16:16:47,310 - __main__ - INFO - 处理supplier: 100002
2025-09-19 16:16:47,313 - __main__ - WARNING - 未找到对应的vendor，跳过: 100002
2025-09-19 16:16:47,313 - __main__ - INFO - 进度: 4/215
2025-09-19 16:16:47,313 - __main__ - INFO - 处理supplier: 100003
2025-09-19 16:16:47,317 - __main__ - INFO - 找到对应vendor: 36222a07-b492-4dfb-a770-d17c44800f6e
2025-09-19 16:16:47,322 - database_manager - INFO - 成功更新vendor: 36222a07-b492-4dfb-a770-d17c44800f6e
2025-09-19 16:16:47,325 - __main__ - INFO - 成功更新vendor: 36222a07-b492-4dfb-a770-d17c44800f6e
2025-09-19 16:16:47,337 - database_manager - INFO - 成功删除vendor联系方式: 36222a07-b492-4dfb-a770-d17c44800f6e
2025-09-19 16:16:47,348 - database_manager - ERROR - 插入邮箱失败: (psycopg2.errors.NotNullViolation) null value in column "email_type" of relation "email" violates not-null constraint
DETAIL:  Failing row contains (d369034e-b75d-45d7-8195-9727af111d84, VENDOR, 36222a07-b492-4dfb-a770-d17c44800f6e, null, <EMAIL>, null, 2025-09-19 16:16:47.345518, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO email (
                id, entity_type, entity_id, email_type, email, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(email_type)s, %(email)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'd369034e-b75d-45d7-8195-9727af111d84', 'entity_type': 'VENDOR', 'entity_id': UUID('36222a07-b492-4dfb-a770-d17c44800f6e'), 'email_type': None, 'email': '<EMAIL>', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 345518), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,348 - __main__ - ERROR - 插入邮箱失败: 36222a07-b492-4dfb-a770-d17c44800f6e
2025-09-19 16:16:47,351 - __main__ - INFO - 成功处理supplier: 100003
2025-09-19 16:16:47,351 - __main__ - INFO - 进度: 5/215
2025-09-19 16:16:47,351 - __main__ - INFO - 处理supplier: 100004
2025-09-19 16:16:47,353 - __main__ - INFO - 找到对应vendor: b3dc7e87-075a-4cf6-8ba2-0f0835918efa
2025-09-19 16:16:47,355 - database_manager - INFO - 成功更新vendor: b3dc7e87-075a-4cf6-8ba2-0f0835918efa
2025-09-19 16:16:47,356 - __main__ - INFO - 成功更新vendor: b3dc7e87-075a-4cf6-8ba2-0f0835918efa
2025-09-19 16:16:47,360 - database_manager - INFO - 成功删除vendor联系方式: b3dc7e87-075a-4cf6-8ba2-0f0835918efa
2025-09-19 16:16:47,372 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (9ecc4b06-b643-4441-87de-f96801baa064, VENDOR, b3dc7e87-075a-4cf6-8ba2-0f0835918efa, null, ************, null, 2025-09-19 16:16:47.371233, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '9ecc4b06-b643-4441-87de-f96801baa064', 'entity_type': 'VENDOR', 'entity_id': UUID('b3dc7e87-075a-4cf6-8ba2-0f0835918efa'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 371233), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,373 - __main__ - ERROR - 插入电话失败: b3dc7e87-075a-4cf6-8ba2-0f0835918efa
2025-09-19 16:16:47,373 - __main__ - INFO - 成功处理supplier: 100004
2025-09-19 16:16:47,373 - __main__ - INFO - 进度: 6/215
2025-09-19 16:16:47,373 - __main__ - INFO - 处理supplier: 100005
2025-09-19 16:16:47,375 - __main__ - INFO - 找到对应vendor: 6bacaf0d-4809-468e-8f6e-8bf3ef0f823d
2025-09-19 16:16:47,376 - database_manager - INFO - 成功更新vendor: 6bacaf0d-4809-468e-8f6e-8bf3ef0f823d
2025-09-19 16:16:47,376 - __main__ - INFO - 成功更新vendor: 6bacaf0d-4809-468e-8f6e-8bf3ef0f823d
2025-09-19 16:16:47,379 - database_manager - INFO - 成功删除vendor联系方式: 6bacaf0d-4809-468e-8f6e-8bf3ef0f823d
2025-09-19 16:16:47,385 - __main__ - INFO - 成功处理supplier: 100005
2025-09-19 16:16:47,385 - __main__ - INFO - 进度: 7/215
2025-09-19 16:16:47,385 - __main__ - INFO - 处理supplier: 100006
2025-09-19 16:16:47,387 - __main__ - INFO - 找到对应vendor: 7588850f-44f4-42fb-919a-db6a4ebdda8e
2025-09-19 16:16:47,388 - database_manager - INFO - 成功更新vendor: 7588850f-44f4-42fb-919a-db6a4ebdda8e
2025-09-19 16:16:47,388 - __main__ - INFO - 成功更新vendor: 7588850f-44f4-42fb-919a-db6a4ebdda8e
2025-09-19 16:16:47,390 - database_manager - INFO - 成功删除vendor联系方式: 7588850f-44f4-42fb-919a-db6a4ebdda8e
2025-09-19 16:16:47,397 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (1b5bdec6-0d17-4bbb-8533-8d620455abfc, VENDOR, 7588850f-44f4-42fb-919a-db6a4ebdda8e, null, ************, null, 2025-09-19 16:16:47.396377, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '1b5bdec6-0d17-4bbb-8533-8d620455abfc', 'entity_type': 'VENDOR', 'entity_id': UUID('7588850f-44f4-42fb-919a-db6a4ebdda8e'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 396377), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,397 - __main__ - ERROR - 插入电话失败: 7588850f-44f4-42fb-919a-db6a4ebdda8e
2025-09-19 16:16:47,398 - __main__ - INFO - 成功处理supplier: 100006
2025-09-19 16:16:47,398 - __main__ - INFO - 进度: 8/215
2025-09-19 16:16:47,398 - __main__ - INFO - 处理supplier: 100007
2025-09-19 16:16:47,399 - __main__ - INFO - 找到对应vendor: 46ae93fc-b12d-4e82-9809-65b2593d0e3e
2025-09-19 16:16:47,400 - database_manager - INFO - 成功更新vendor: 46ae93fc-b12d-4e82-9809-65b2593d0e3e
2025-09-19 16:16:47,400 - __main__ - INFO - 成功更新vendor: 46ae93fc-b12d-4e82-9809-65b2593d0e3e
2025-09-19 16:16:47,403 - database_manager - INFO - 成功删除vendor联系方式: 46ae93fc-b12d-4e82-9809-65b2593d0e3e
2025-09-19 16:16:47,407 - __main__ - INFO - 成功处理supplier: 100007
2025-09-19 16:16:47,407 - __main__ - INFO - 进度: 9/215
2025-09-19 16:16:47,407 - __main__ - INFO - 处理supplier: 100008
2025-09-19 16:16:47,408 - __main__ - INFO - 找到对应vendor: cf6a0a06-0aa3-42a9-bd7d-df30c93bd582
2025-09-19 16:16:47,408 - data_mapper - WARNING - 邮箱地址无效，跳过
2025-09-19 16:16:47,410 - database_manager - INFO - 成功更新vendor: cf6a0a06-0aa3-42a9-bd7d-df30c93bd582
2025-09-19 16:16:47,411 - __main__ - INFO - 成功更新vendor: cf6a0a06-0aa3-42a9-bd7d-df30c93bd582
2025-09-19 16:16:47,414 - database_manager - INFO - 成功删除vendor联系方式: cf6a0a06-0aa3-42a9-bd7d-df30c93bd582
2025-09-19 16:16:47,420 - database_manager - ERROR - 插入邮箱失败: (psycopg2.errors.NotNullViolation) null value in column "email_type" of relation "email" violates not-null constraint
DETAIL:  Failing row contains (828b819f-f32c-4e1f-a631-fe148a8c6045, VENDOR, cf6a0a06-0aa3-42a9-bd7d-df30c93bd582, null, <EMAIL>, null, 2025-09-19 16:16:47.419275, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO email (
                id, entity_type, entity_id, email_type, email, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(email_type)s, %(email)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '828b819f-f32c-4e1f-a631-fe148a8c6045', 'entity_type': 'VENDOR', 'entity_id': UUID('cf6a0a06-0aa3-42a9-bd7d-df30c93bd582'), 'email_type': None, 'email': '<EMAIL>', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 419275), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,420 - __main__ - ERROR - 插入邮箱失败: cf6a0a06-0aa3-42a9-bd7d-df30c93bd582
2025-09-19 16:16:47,422 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (6f278df8-acb0-43d2-bcf3-6693a0467e3a, VENDOR, cf6a0a06-0aa3-42a9-bd7d-df30c93bd582, null, ************, null, 2025-09-19 16:16:47.420881, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '6f278df8-acb0-43d2-bcf3-6693a0467e3a', 'entity_type': 'VENDOR', 'entity_id': UUID('cf6a0a06-0aa3-42a9-bd7d-df30c93bd582'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 420881), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,422 - __main__ - ERROR - 插入电话失败: cf6a0a06-0aa3-42a9-bd7d-df30c93bd582
2025-09-19 16:16:47,423 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (04debc56-512b-46a7-884e-ec35a8c607c8, VENDOR, cf6a0a06-0aa3-42a9-bd7d-df30c93bd582, null, ************, null, 2025-09-19 16:16:47.422571, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '04debc56-512b-46a7-884e-ec35a8c607c8', 'entity_type': 'VENDOR', 'entity_id': UUID('cf6a0a06-0aa3-42a9-bd7d-df30c93bd582'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 422571), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,423 - __main__ - ERROR - 插入电话失败: cf6a0a06-0aa3-42a9-bd7d-df30c93bd582
2025-09-19 16:16:47,424 - __main__ - INFO - 成功处理supplier: 100008
2025-09-19 16:16:47,424 - __main__ - INFO - 进度: 10/215
2025-09-19 16:16:47,424 - __main__ - INFO - 处理supplier: 100009
2025-09-19 16:16:47,425 - __main__ - INFO - 找到对应vendor: 1bbde520-1750-4cb4-a785-03b4dbe672aa
2025-09-19 16:16:47,426 - database_manager - INFO - 成功更新vendor: 1bbde520-1750-4cb4-a785-03b4dbe672aa
2025-09-19 16:16:47,427 - __main__ - INFO - 成功更新vendor: 1bbde520-1750-4cb4-a785-03b4dbe672aa
2025-09-19 16:16:47,429 - database_manager - INFO - 成功删除vendor联系方式: 1bbde520-1750-4cb4-a785-03b4dbe672aa
2025-09-19 16:16:47,433 - __main__ - INFO - 成功处理supplier: 100009
2025-09-19 16:16:47,434 - __main__ - INFO - 进度: 11/215
2025-09-19 16:16:47,434 - __main__ - INFO - 处理supplier: 100010
2025-09-19 16:16:47,435 - __main__ - INFO - 找到对应vendor: da473beb-adcc-4a32-9b8d-bea8bdf6c741
2025-09-19 16:16:47,436 - database_manager - INFO - 成功更新vendor: da473beb-adcc-4a32-9b8d-bea8bdf6c741
2025-09-19 16:16:47,436 - __main__ - INFO - 成功更新vendor: da473beb-adcc-4a32-9b8d-bea8bdf6c741
2025-09-19 16:16:47,438 - database_manager - INFO - 成功删除vendor联系方式: da473beb-adcc-4a32-9b8d-bea8bdf6c741
2025-09-19 16:16:47,444 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (a9b47af9-bac2-435c-a1e9-f511a518dc4b, VENDOR, da473beb-adcc-4a32-9b8d-bea8bdf6c741, null, ************, null, 2025-09-19 16:16:47.443408, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'a9b47af9-bac2-435c-a1e9-f511a518dc4b', 'entity_type': 'VENDOR', 'entity_id': UUID('da473beb-adcc-4a32-9b8d-bea8bdf6c741'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 443408), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,444 - __main__ - ERROR - 插入电话失败: da473beb-adcc-4a32-9b8d-bea8bdf6c741
2025-09-19 16:16:47,446 - database_manager - ERROR - 插入网址失败: (psycopg2.errors.NotNullViolation) null value in column "web_address_type" of relation "web_address" violates not-null constraint
DETAIL:  Failing row contains (4478e45a-c706-41fe-bf2e-50b59ac819ab, VENDOR, da473beb-adcc-4a32-9b8d-bea8bdf6c741, null, https://www.pepsicopartners.com/pepsico/en/USD/, 2025-09-19 16:16:47.444833, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO web_address (
                id, entity_type, entity_id, web_address_type, web_address,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(web_address_type)s, %(web_address)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '4478e45a-c706-41fe-bf2e-50b59ac819ab', 'entity_type': 'VENDOR', 'entity_id': UUID('da473beb-adcc-4a32-9b8d-bea8bdf6c741'), 'web_address_type': None, 'web_address': 'https://www.pepsicopartners.com/pepsico/en/USD/', 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 444833), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,446 - __main__ - ERROR - 插入网址失败: da473beb-adcc-4a32-9b8d-bea8bdf6c741
2025-09-19 16:16:47,446 - __main__ - INFO - 成功处理supplier: 100010
2025-09-19 16:16:47,446 - __main__ - INFO - 进度: 12/215
2025-09-19 16:16:47,446 - __main__ - INFO - 处理supplier: 100011
2025-09-19 16:16:47,447 - __main__ - INFO - 找到对应vendor: 02ba9b84-46cd-41f3-8510-24956ed923a2
2025-09-19 16:16:47,448 - data_mapper - WARNING - 邮箱地址无效，跳过
2025-09-19 16:16:47,449 - database_manager - INFO - 成功更新vendor: 02ba9b84-46cd-41f3-8510-24956ed923a2
2025-09-19 16:16:47,449 - __main__ - INFO - 成功更新vendor: 02ba9b84-46cd-41f3-8510-24956ed923a2
2025-09-19 16:16:47,451 - database_manager - INFO - 成功删除vendor联系方式: 02ba9b84-46cd-41f3-8510-24956ed923a2
2025-09-19 16:16:47,456 - database_manager - ERROR - 插入邮箱失败: (psycopg2.errors.NotNullViolation) null value in column "email_type" of relation "email" violates not-null constraint
DETAIL:  Failing row contains (cda2736d-7bd9-48e6-8c29-a4466f2aea78, VENDOR, 02ba9b84-46cd-41f3-8510-24956ed923a2, null, <EMAIL>, null, 2025-09-19 16:16:47.455014, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO email (
                id, entity_type, entity_id, email_type, email, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(email_type)s, %(email)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'cda2736d-7bd9-48e6-8c29-a4466f2aea78', 'entity_type': 'VENDOR', 'entity_id': UUID('02ba9b84-46cd-41f3-8510-24956ed923a2'), 'email_type': None, 'email': '<EMAIL>', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 455014), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,456 - __main__ - ERROR - 插入邮箱失败: 02ba9b84-46cd-41f3-8510-24956ed923a2
2025-09-19 16:16:47,457 - __main__ - INFO - 成功处理supplier: 100011
2025-09-19 16:16:47,457 - __main__ - INFO - 进度: 13/215
2025-09-19 16:16:47,457 - __main__ - INFO - 处理supplier: 100012
2025-09-19 16:16:47,458 - __main__ - INFO - 找到对应vendor: 9b1de203-c7c6-43b7-b558-94e0f1eaadee
2025-09-19 16:16:47,458 - data_mapper - WARNING - 电话号码长度不符合要求，跳过
2025-09-19 16:16:47,459 - database_manager - INFO - 成功更新vendor: 9b1de203-c7c6-43b7-b558-94e0f1eaadee
2025-09-19 16:16:47,460 - __main__ - INFO - 成功更新vendor: 9b1de203-c7c6-43b7-b558-94e0f1eaadee
2025-09-19 16:16:47,462 - database_manager - INFO - 成功删除vendor联系方式: 9b1de203-c7c6-43b7-b558-94e0f1eaadee
2025-09-19 16:16:47,466 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (62e74905-59af-4ab8-b3e1-68e1eb5d38e2, VENDOR, 9b1de203-c7c6-43b7-b558-94e0f1eaadee, null, ************, null, 2025-09-19 16:16:47.465527, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '62e74905-59af-4ab8-b3e1-68e1eb5d38e2', 'entity_type': 'VENDOR', 'entity_id': UUID('9b1de203-c7c6-43b7-b558-94e0f1eaadee'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 465527), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,466 - __main__ - ERROR - 插入电话失败: 9b1de203-c7c6-43b7-b558-94e0f1eaadee
2025-09-19 16:16:47,466 - __main__ - INFO - 成功处理supplier: 100012
2025-09-19 16:16:47,466 - __main__ - INFO - 进度: 14/215
2025-09-19 16:16:47,466 - __main__ - INFO - 处理supplier: 100013
2025-09-19 16:16:47,467 - __main__ - INFO - 找到对应vendor: f568c5cb-1461-4129-a6a5-************
2025-09-19 16:16:47,467 - data_mapper - WARNING - 电话号码长度不符合要求，跳过
2025-09-19 16:16:47,468 - database_manager - INFO - 成功更新vendor: f568c5cb-1461-4129-a6a5-************
2025-09-19 16:16:47,469 - __main__ - INFO - 成功更新vendor: f568c5cb-1461-4129-a6a5-************
2025-09-19 16:16:47,470 - database_manager - INFO - 成功删除vendor联系方式: f568c5cb-1461-4129-a6a5-************
2025-09-19 16:16:47,477 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (6cc41fdc-3334-453c-90ce-55f840eeda54, VENDOR, f568c5cb-1461-4129-a6a5-************, null, ************, null, 2025-09-19 16:16:47.476486, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '6cc41fdc-3334-453c-90ce-55f840eeda54', 'entity_type': 'VENDOR', 'entity_id': UUID('f568c5cb-1461-4129-a6a5-************'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 476486), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,477 - __main__ - ERROR - 插入电话失败: f568c5cb-1461-4129-a6a5-************
2025-09-19 16:16:47,477 - __main__ - INFO - 成功处理supplier: 100013
2025-09-19 16:16:47,477 - __main__ - INFO - 进度: 15/215
2025-09-19 16:16:47,477 - __main__ - INFO - 处理supplier: 100014
2025-09-19 16:16:47,478 - __main__ - INFO - 找到对应vendor: 4777608d-190a-43ba-a3bf-0a85761711a0
2025-09-19 16:16:47,479 - database_manager - INFO - 成功更新vendor: 4777608d-190a-43ba-a3bf-0a85761711a0
2025-09-19 16:16:47,479 - __main__ - INFO - 成功更新vendor: 4777608d-190a-43ba-a3bf-0a85761711a0
2025-09-19 16:16:47,480 - database_manager - INFO - 成功删除vendor联系方式: 4777608d-190a-43ba-a3bf-0a85761711a0
2025-09-19 16:16:47,485 - __main__ - INFO - 成功处理supplier: 100014
2025-09-19 16:16:47,485 - __main__ - INFO - 进度: 16/215
2025-09-19 16:16:47,485 - __main__ - INFO - 处理supplier: 100015
2025-09-19 16:16:47,486 - __main__ - INFO - 找到对应vendor: 469a13db-9d53-44a9-9a8e-bb87af0e1fef
2025-09-19 16:16:47,486 - database_manager - INFO - 成功更新vendor: 469a13db-9d53-44a9-9a8e-bb87af0e1fef
2025-09-19 16:16:47,487 - __main__ - INFO - 成功更新vendor: 469a13db-9d53-44a9-9a8e-bb87af0e1fef
2025-09-19 16:16:47,488 - database_manager - INFO - 成功删除vendor联系方式: 469a13db-9d53-44a9-9a8e-bb87af0e1fef
2025-09-19 16:16:47,489 - __main__ - INFO - 成功处理supplier: 100015
2025-09-19 16:16:47,489 - __main__ - INFO - 进度: 17/215
2025-09-19 16:16:47,489 - __main__ - INFO - 处理supplier: 100016
2025-09-19 16:16:47,490 - __main__ - INFO - 找到对应vendor: cac2da56-067f-48f7-9a3a-b626cf2e3ccb
2025-09-19 16:16:47,490 - database_manager - INFO - 成功更新vendor: cac2da56-067f-48f7-9a3a-b626cf2e3ccb
2025-09-19 16:16:47,490 - __main__ - INFO - 成功更新vendor: cac2da56-067f-48f7-9a3a-b626cf2e3ccb
2025-09-19 16:16:47,492 - database_manager - INFO - 成功删除vendor联系方式: cac2da56-067f-48f7-9a3a-b626cf2e3ccb
2025-09-19 16:16:47,492 - __main__ - INFO - 成功处理supplier: 100016
2025-09-19 16:16:47,492 - __main__ - INFO - 进度: 18/215
2025-09-19 16:16:47,493 - __main__ - INFO - 处理supplier: 100017
2025-09-19 16:16:47,493 - __main__ - WARNING - 未找到对应的vendor，跳过: 100017
2025-09-19 16:16:47,493 - __main__ - INFO - 进度: 19/215
2025-09-19 16:16:47,493 - __main__ - INFO - 处理supplier: 100018
2025-09-19 16:16:47,494 - __main__ - INFO - 找到对应vendor: b72fc2f8-7579-4793-8e11-6dd027cf7cbd
2025-09-19 16:16:47,495 - database_manager - INFO - 成功更新vendor: b72fc2f8-7579-4793-8e11-6dd027cf7cbd
2025-09-19 16:16:47,495 - __main__ - INFO - 成功更新vendor: b72fc2f8-7579-4793-8e11-6dd027cf7cbd
2025-09-19 16:16:47,496 - database_manager - INFO - 成功删除vendor联系方式: b72fc2f8-7579-4793-8e11-6dd027cf7cbd
2025-09-19 16:16:47,500 - database_manager - ERROR - 插入地址失败: (psycopg2.errors.NotNullViolation) null value in column "purpose" of relation "address" violates not-null constraint
DETAIL:  Failing row contains (d59ec289-b396-421b-b583-c7b847eaa4a8, VENDOR, b72fc2f8-7579-4793-8e11-6dd027cf7cbd, 3422 Garfield Ave., Commerce, CA, 90040, null, null, null, null, 2025-09-19 16:16:47.498874, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO address (
                id, entity_type, entity_id, street_address, city, state, 
                postal_code, country, directions, purpose, additional_lines,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(street_address)s, %(city)s, %(state)s,
                %(postal_code)s, %(country)s, %(directions)s, %(purpose)s, %(additional_lines)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'd59ec289-b396-421b-b583-c7b847eaa4a8', 'entity_type': 'VENDOR', 'entity_id': UUID('b72fc2f8-7579-4793-8e11-6dd027cf7cbd'), 'street_address': '3422 Garfield Ave.', 'city': 'Commerce', 'state': 'CA', 'postal_code': '90040', 'country': None, 'directions': None, 'purpose': None, 'additional_lines': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 498874), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,500 - __main__ - ERROR - 插入地址失败: b72fc2f8-7579-4793-8e11-6dd027cf7cbd
2025-09-19 16:16:47,501 - __main__ - INFO - 成功处理supplier: 100018
2025-09-19 16:16:47,501 - __main__ - INFO - 进度: 20/215
2025-09-19 16:16:47,501 - __main__ - INFO - 处理supplier: 100019
2025-09-19 16:16:47,502 - __main__ - INFO - 找到对应vendor: a76a26d4-7597-49af-8b55-23fdbd5dc8d2
2025-09-19 16:16:47,503 - database_manager - INFO - 成功更新vendor: a76a26d4-7597-49af-8b55-23fdbd5dc8d2
2025-09-19 16:16:47,504 - __main__ - INFO - 成功更新vendor: a76a26d4-7597-49af-8b55-23fdbd5dc8d2
2025-09-19 16:16:47,506 - database_manager - INFO - 成功删除vendor联系方式: a76a26d4-7597-49af-8b55-23fdbd5dc8d2
2025-09-19 16:16:47,508 - database_manager - ERROR - 插入地址失败: (psycopg2.errors.NotNullViolation) null value in column "purpose" of relation "address" violates not-null constraint
DETAIL:  Failing row contains (f66674da-030e-45de-ba7c-f81e825c269e, VENDOR, a76a26d4-7597-49af-8b55-23fdbd5dc8d2, 6079 Rickenbacker Rd., Commerce, CA, 90040, USA, null, null, null, 2025-09-19 16:16:47.507327, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO address (
                id, entity_type, entity_id, street_address, city, state, 
                postal_code, country, directions, purpose, additional_lines,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(street_address)s, %(city)s, %(state)s,
                %(postal_code)s, %(country)s, %(directions)s, %(purpose)s, %(additional_lines)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'f66674da-030e-45de-ba7c-f81e825c269e', 'entity_type': 'VENDOR', 'entity_id': UUID('a76a26d4-7597-49af-8b55-23fdbd5dc8d2'), 'street_address': '6079 Rickenbacker Rd.', 'city': 'Commerce', 'state': 'CA', 'postal_code': '90040', 'country': 'USA', 'directions': None, 'purpose': None, 'additional_lines': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 507327), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,508 - __main__ - ERROR - 插入地址失败: a76a26d4-7597-49af-8b55-23fdbd5dc8d2
2025-09-19 16:16:47,510 - __main__ - INFO - 成功处理supplier: 100019
2025-09-19 16:16:47,510 - __main__ - INFO - 进度: 21/215
2025-09-19 16:16:47,510 - __main__ - INFO - 处理supplier: 100020
2025-09-19 16:16:47,511 - __main__ - INFO - 找到对应vendor: 4c7dc747-4d13-47f8-8499-65acb0c8a71c
2025-09-19 16:16:47,511 - database_manager - INFO - 成功更新vendor: 4c7dc747-4d13-47f8-8499-65acb0c8a71c
2025-09-19 16:16:47,511 - __main__ - INFO - 成功更新vendor: 4c7dc747-4d13-47f8-8499-65acb0c8a71c
2025-09-19 16:16:47,513 - database_manager - INFO - 成功删除vendor联系方式: 4c7dc747-4d13-47f8-8499-65acb0c8a71c
2025-09-19 16:16:47,514 - database_manager - ERROR - 插入地址失败: (psycopg2.errors.NotNullViolation) null value in column "purpose" of relation "address" violates not-null constraint
DETAIL:  Failing row contains (e20bd7b4-6d15-44d7-a347-cf17cce1f0d0, VENDOR, 4c7dc747-4d13-47f8-8499-65acb0c8a71c, 2701 Carrier Ave., Commerce, CA, 90040, null, null, null, null, 2025-09-19 16:16:47.514155, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO address (
                id, entity_type, entity_id, street_address, city, state, 
                postal_code, country, directions, purpose, additional_lines,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(street_address)s, %(city)s, %(state)s,
                %(postal_code)s, %(country)s, %(directions)s, %(purpose)s, %(additional_lines)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'e20bd7b4-6d15-44d7-a347-cf17cce1f0d0', 'entity_type': 'VENDOR', 'entity_id': UUID('4c7dc747-4d13-47f8-8499-65acb0c8a71c'), 'street_address': '2701 Carrier Ave.', 'city': 'Commerce', 'state': 'CA', 'postal_code': '90040', 'country': None, 'directions': None, 'purpose': None, 'additional_lines': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 514155), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,514 - __main__ - ERROR - 插入地址失败: 4c7dc747-4d13-47f8-8499-65acb0c8a71c
2025-09-19 16:16:47,516 - __main__ - INFO - 成功处理supplier: 100020
2025-09-19 16:16:47,516 - __main__ - INFO - 进度: 22/215
2025-09-19 16:16:47,516 - __main__ - INFO - 处理supplier: 100021
2025-09-19 16:16:47,517 - __main__ - WARNING - 未找到对应的vendor，跳过: 100021
2025-09-19 16:16:47,517 - __main__ - INFO - 进度: 23/215
2025-09-19 16:16:47,517 - __main__ - INFO - 处理supplier: 100022
2025-09-19 16:16:47,517 - __main__ - INFO - 找到对应vendor: 2c19c67d-cf3d-482a-b407-30b10b45c9b0
2025-09-19 16:16:47,518 - data_mapper - WARNING - 电话号码长度不符合要求，跳过
2025-09-19 16:16:47,518 - database_manager - INFO - 成功更新vendor: 2c19c67d-cf3d-482a-b407-30b10b45c9b0
2025-09-19 16:16:47,519 - __main__ - INFO - 成功更新vendor: 2c19c67d-cf3d-482a-b407-30b10b45c9b0
2025-09-19 16:16:47,520 - database_manager - INFO - 成功删除vendor联系方式: 2c19c67d-cf3d-482a-b407-30b10b45c9b0
2025-09-19 16:16:47,522 - __main__ - INFO - 成功处理supplier: 100022
2025-09-19 16:16:47,522 - __main__ - INFO - 进度: 24/215
2025-09-19 16:16:47,522 - __main__ - INFO - 处理supplier: 100023
2025-09-19 16:16:47,523 - __main__ - INFO - 找到对应vendor: 1496b3f0-841f-4bad-b7b4-82363d8b89ae
2025-09-19 16:16:47,523 - database_manager - INFO - 成功更新vendor: 1496b3f0-841f-4bad-b7b4-82363d8b89ae
2025-09-19 16:16:47,524 - __main__ - INFO - 成功更新vendor: 1496b3f0-841f-4bad-b7b4-82363d8b89ae
2025-09-19 16:16:47,525 - database_manager - INFO - 成功删除vendor联系方式: 1496b3f0-841f-4bad-b7b4-82363d8b89ae
2025-09-19 16:16:47,528 - __main__ - INFO - 成功处理supplier: 100023
2025-09-19 16:16:47,528 - __main__ - INFO - 进度: 25/215
2025-09-19 16:16:47,528 - __main__ - INFO - 处理supplier: 100024
2025-09-19 16:16:47,529 - __main__ - WARNING - 未找到对应的vendor，跳过: 100024
2025-09-19 16:16:47,529 - __main__ - INFO - 进度: 26/215
2025-09-19 16:16:47,529 - __main__ - INFO - 处理supplier: 100025
2025-09-19 16:16:47,529 - __main__ - INFO - 找到对应vendor: 4b2e8574-f674-46ec-979e-a1f6d35ce261
2025-09-19 16:16:47,530 - database_manager - INFO - 成功更新vendor: 4b2e8574-f674-46ec-979e-a1f6d35ce261
2025-09-19 16:16:47,530 - __main__ - INFO - 成功更新vendor: 4b2e8574-f674-46ec-979e-a1f6d35ce261
2025-09-19 16:16:47,531 - database_manager - INFO - 成功删除vendor联系方式: 4b2e8574-f674-46ec-979e-a1f6d35ce261
2025-09-19 16:16:47,534 - __main__ - INFO - 成功处理supplier: 100025
2025-09-19 16:16:47,534 - __main__ - INFO - 进度: 27/215
2025-09-19 16:16:47,535 - __main__ - INFO - 处理supplier: 100026
2025-09-19 16:16:47,535 - __main__ - WARNING - 未找到对应的vendor，跳过: 100026
2025-09-19 16:16:47,535 - __main__ - INFO - 进度: 28/215
2025-09-19 16:16:47,535 - __main__ - INFO - 处理supplier: 100027
2025-09-19 16:16:47,536 - __main__ - INFO - 找到对应vendor: 2d040d89-3a89-4f0a-a74d-7dba1452b67c
2025-09-19 16:16:47,536 - database_manager - INFO - 成功更新vendor: 2d040d89-3a89-4f0a-a74d-7dba1452b67c
2025-09-19 16:16:47,537 - __main__ - INFO - 成功更新vendor: 2d040d89-3a89-4f0a-a74d-7dba1452b67c
2025-09-19 16:16:47,538 - database_manager - INFO - 成功删除vendor联系方式: 2d040d89-3a89-4f0a-a74d-7dba1452b67c
2025-09-19 16:16:47,539 - __main__ - INFO - 成功处理supplier: 100027
2025-09-19 16:16:47,539 - __main__ - INFO - 进度: 29/215
2025-09-19 16:16:47,539 - __main__ - INFO - 处理supplier: 100028
2025-09-19 16:16:47,539 - __main__ - INFO - 找到对应vendor: db23b3e1-dd8f-4c48-b9df-4afde6e2db70
2025-09-19 16:16:47,540 - database_manager - INFO - 成功更新vendor: db23b3e1-dd8f-4c48-b9df-4afde6e2db70
2025-09-19 16:16:47,540 - __main__ - INFO - 成功更新vendor: db23b3e1-dd8f-4c48-b9df-4afde6e2db70
2025-09-19 16:16:47,541 - database_manager - INFO - 成功删除vendor联系方式: db23b3e1-dd8f-4c48-b9df-4afde6e2db70
2025-09-19 16:16:47,542 - __main__ - INFO - 成功处理supplier: 100028
2025-09-19 16:16:47,542 - __main__ - INFO - 进度: 30/215
2025-09-19 16:16:47,542 - __main__ - INFO - 处理supplier: 100029
2025-09-19 16:16:47,543 - __main__ - WARNING - 未找到对应的vendor，跳过: 100029
2025-09-19 16:16:47,543 - __main__ - INFO - 进度: 31/215
2025-09-19 16:16:47,543 - __main__ - INFO - 处理supplier: 100030
2025-09-19 16:16:47,544 - __main__ - INFO - 找到对应vendor: fbfbc84f-f627-45c2-9270-0c782fc90874
2025-09-19 16:16:47,544 - database_manager - INFO - 成功更新vendor: fbfbc84f-f627-45c2-9270-0c782fc90874
2025-09-19 16:16:47,545 - __main__ - INFO - 成功更新vendor: fbfbc84f-f627-45c2-9270-0c782fc90874
2025-09-19 16:16:47,546 - database_manager - INFO - 成功删除vendor联系方式: fbfbc84f-f627-45c2-9270-0c782fc90874
2025-09-19 16:16:47,549 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (8dab7653-3c49-4dd8-8465-09c69df1eedf, VENDOR, fbfbc84f-f627-45c2-9270-0c782fc90874, null, ************, null, 2025-09-19 16:16:47.548976, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '8dab7653-3c49-4dd8-8465-09c69df1eedf', 'entity_type': 'VENDOR', 'entity_id': UUID('fbfbc84f-f627-45c2-9270-0c782fc90874'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 548976), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,549 - __main__ - ERROR - 插入电话失败: fbfbc84f-f627-45c2-9270-0c782fc90874
2025-09-19 16:16:47,550 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (81eb9e97-77bb-476b-b842-8ff158fa1869, VENDOR, fbfbc84f-f627-45c2-9270-0c782fc90874, null, ************, null, 2025-09-19 16:16:47.549795, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '81eb9e97-77bb-476b-b842-8ff158fa1869', 'entity_type': 'VENDOR', 'entity_id': UUID('fbfbc84f-f627-45c2-9270-0c782fc90874'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 549795), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,550 - __main__ - ERROR - 插入电话失败: fbfbc84f-f627-45c2-9270-0c782fc90874
2025-09-19 16:16:47,550 - __main__ - INFO - 成功处理supplier: 100030
2025-09-19 16:16:47,550 - __main__ - INFO - 进度: 32/215
2025-09-19 16:16:47,550 - __main__ - INFO - 处理supplier: 100031
2025-09-19 16:16:47,551 - __main__ - INFO - 找到对应vendor: d817b282-18a9-468c-80a0-6a55dee192ec
2025-09-19 16:16:47,551 - database_manager - INFO - 成功更新vendor: d817b282-18a9-468c-80a0-6a55dee192ec
2025-09-19 16:16:47,552 - __main__ - INFO - 成功更新vendor: d817b282-18a9-468c-80a0-6a55dee192ec
2025-09-19 16:16:47,553 - database_manager - INFO - 成功删除vendor联系方式: d817b282-18a9-468c-80a0-6a55dee192ec
2025-09-19 16:16:47,555 - __main__ - INFO - 成功处理supplier: 100031
2025-09-19 16:16:47,555 - __main__ - INFO - 进度: 33/215
2025-09-19 16:16:47,555 - __main__ - INFO - 处理supplier: 100032
2025-09-19 16:16:47,556 - __main__ - INFO - 找到对应vendor: 3e8feedf-3ff5-437e-afb5-6454f5207e86
2025-09-19 16:16:47,556 - database_manager - INFO - 成功更新vendor: 3e8feedf-3ff5-437e-afb5-6454f5207e86
2025-09-19 16:16:47,557 - __main__ - INFO - 成功更新vendor: 3e8feedf-3ff5-437e-afb5-6454f5207e86
2025-09-19 16:16:47,558 - database_manager - INFO - 成功删除vendor联系方式: 3e8feedf-3ff5-437e-afb5-6454f5207e86
2025-09-19 16:16:47,561 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (1d5dd1db-dc81-4b9b-bb2b-98b8d04e2081, VENDOR, 3e8feedf-3ff5-437e-afb5-6454f5207e86, null, ************, null, 2025-09-19 16:16:47.560552, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '1d5dd1db-dc81-4b9b-bb2b-98b8d04e2081', 'entity_type': 'VENDOR', 'entity_id': UUID('3e8feedf-3ff5-437e-afb5-6454f5207e86'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 560552), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,561 - __main__ - ERROR - 插入电话失败: 3e8feedf-3ff5-437e-afb5-6454f5207e86
2025-09-19 16:16:47,562 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (e930ab1c-bcb8-4947-9a28-5a3cd7f54f60, VENDOR, 3e8feedf-3ff5-437e-afb5-6454f5207e86, null, ************, null, 2025-09-19 16:16:47.561503, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'e930ab1c-bcb8-4947-9a28-5a3cd7f54f60', 'entity_type': 'VENDOR', 'entity_id': UUID('3e8feedf-3ff5-437e-afb5-6454f5207e86'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 561503), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,562 - __main__ - ERROR - 插入电话失败: 3e8feedf-3ff5-437e-afb5-6454f5207e86
2025-09-19 16:16:47,562 - __main__ - INFO - 成功处理supplier: 100032
2025-09-19 16:16:47,562 - __main__ - INFO - 进度: 34/215
2025-09-19 16:16:47,562 - __main__ - INFO - 处理supplier: 100033
2025-09-19 16:16:47,563 - __main__ - WARNING - 未找到对应的vendor，跳过: 100033
2025-09-19 16:16:47,563 - __main__ - INFO - 进度: 35/215
2025-09-19 16:16:47,563 - __main__ - INFO - 处理supplier: 100034
2025-09-19 16:16:47,563 - __main__ - INFO - 找到对应vendor: 2de2123b-02a1-47b5-9dc0-2fa3d2fc371d
2025-09-19 16:16:47,563 - data_mapper - WARNING - 电话号码长度不符合要求，跳过
2025-09-19 16:16:47,564 - database_manager - INFO - 成功更新vendor: 2de2123b-02a1-47b5-9dc0-2fa3d2fc371d
2025-09-19 16:16:47,564 - __main__ - INFO - 成功更新vendor: 2de2123b-02a1-47b5-9dc0-2fa3d2fc371d
2025-09-19 16:16:47,565 - database_manager - INFO - 成功删除vendor联系方式: 2de2123b-02a1-47b5-9dc0-2fa3d2fc371d
2025-09-19 16:16:47,571 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (6066734a-53dc-4af6-9d1e-261ab2a6aeab, VENDOR, 2de2123b-02a1-47b5-9dc0-2fa3d2fc371d, null, ************, null, 2025-09-19 16:16:47.570389, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '6066734a-53dc-4af6-9d1e-261ab2a6aeab', 'entity_type': 'VENDOR', 'entity_id': UUID('2de2123b-02a1-47b5-9dc0-2fa3d2fc371d'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 570389), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,571 - __main__ - ERROR - 插入电话失败: 2de2123b-02a1-47b5-9dc0-2fa3d2fc371d
2025-09-19 16:16:47,571 - database_manager - ERROR - 插入网址失败: (psycopg2.errors.NotNullViolation) null value in column "web_address_type" of relation "web_address" violates not-null constraint
DETAIL:  Failing row contains (54cc5b69-afd8-40af-ab4f-0b85ce339240, VENDOR, 2de2123b-02a1-47b5-9dc0-2fa3d2fc371d, null, www.alliedwestpaper.com, 2025-09-19 16:16:47.571152, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO web_address (
                id, entity_type, entity_id, web_address_type, web_address,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(web_address_type)s, %(web_address)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '54cc5b69-afd8-40af-ab4f-0b85ce339240', 'entity_type': 'VENDOR', 'entity_id': UUID('2de2123b-02a1-47b5-9dc0-2fa3d2fc371d'), 'web_address_type': None, 'web_address': 'www.alliedwestpaper.com', 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 571152), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,571 - __main__ - ERROR - 插入网址失败: 2de2123b-02a1-47b5-9dc0-2fa3d2fc371d
2025-09-19 16:16:47,571 - __main__ - INFO - 成功处理supplier: 100034
2025-09-19 16:16:47,571 - __main__ - INFO - 进度: 36/215
2025-09-19 16:16:47,571 - __main__ - INFO - 处理supplier: 100035
2025-09-19 16:16:47,572 - __main__ - INFO - 找到对应vendor: dbf0440d-a979-4a8e-a066-96b8dd69ecb5
2025-09-19 16:16:47,573 - database_manager - INFO - 成功更新vendor: dbf0440d-a979-4a8e-a066-96b8dd69ecb5
2025-09-19 16:16:47,573 - __main__ - INFO - 成功更新vendor: dbf0440d-a979-4a8e-a066-96b8dd69ecb5
2025-09-19 16:16:47,574 - database_manager - INFO - 成功删除vendor联系方式: dbf0440d-a979-4a8e-a066-96b8dd69ecb5
2025-09-19 16:16:47,578 - database_manager - ERROR - 插入网址失败: (psycopg2.errors.NotNullViolation) null value in column "web_address_type" of relation "web_address" violates not-null constraint
DETAIL:  Failing row contains (3618061d-ad43-449f-aaca-38f93830b2cb, VENDOR, dbf0440d-a979-4a8e-a066-96b8dd69ecb5, null, https://californiasnackfoods.com/, 2025-09-19 16:16:47.577688, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO web_address (
                id, entity_type, entity_id, web_address_type, web_address,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(web_address_type)s, %(web_address)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '3618061d-ad43-449f-aaca-38f93830b2cb', 'entity_type': 'VENDOR', 'entity_id': UUID('dbf0440d-a979-4a8e-a066-96b8dd69ecb5'), 'web_address_type': None, 'web_address': 'https://californiasnackfoods.com/', 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 577688), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,578 - __main__ - ERROR - 插入网址失败: dbf0440d-a979-4a8e-a066-96b8dd69ecb5
2025-09-19 16:16:47,578 - __main__ - INFO - 成功处理supplier: 100035
2025-09-19 16:16:47,578 - __main__ - INFO - 进度: 37/215
2025-09-19 16:16:47,578 - __main__ - INFO - 处理supplier: 100036
2025-09-19 16:16:47,579 - __main__ - WARNING - 未找到对应的vendor，跳过: 100036
2025-09-19 16:16:47,579 - __main__ - INFO - 进度: 38/215
2025-09-19 16:16:47,579 - __main__ - INFO - 处理supplier: 100037
2025-09-19 16:16:47,580 - __main__ - WARNING - 未找到对应的vendor，跳过: 100037
2025-09-19 16:16:47,580 - __main__ - INFO - 进度: 39/215
2025-09-19 16:16:47,580 - __main__ - INFO - 处理supplier: 100038
2025-09-19 16:16:47,580 - __main__ - WARNING - 未找到对应的vendor，跳过: 100038
2025-09-19 16:16:47,580 - __main__ - INFO - 进度: 40/215
2025-09-19 16:16:47,580 - __main__ - INFO - 处理supplier: 100039
2025-09-19 16:16:47,581 - __main__ - WARNING - 未找到对应的vendor，跳过: 100039
2025-09-19 16:16:47,581 - __main__ - INFO - 进度: 41/215
2025-09-19 16:16:47,581 - __main__ - INFO - 处理supplier: 100040
2025-09-19 16:16:47,581 - __main__ - INFO - 找到对应vendor: 3b9b36e1-4414-434a-bfc0-a000e0ea1c5f
2025-09-19 16:16:47,581 - data_mapper - WARNING - 城市信息不完整，跳过
2025-09-19 16:16:47,582 - database_manager - INFO - 成功更新vendor: 3b9b36e1-4414-434a-bfc0-a000e0ea1c5f
2025-09-19 16:16:47,582 - __main__ - INFO - 成功更新vendor: 3b9b36e1-4414-434a-bfc0-a000e0ea1c5f
2025-09-19 16:16:47,583 - database_manager - INFO - 成功删除vendor联系方式: 3b9b36e1-4414-434a-bfc0-a000e0ea1c5f
2025-09-19 16:16:47,587 - __main__ - INFO - 成功处理supplier: 100040
2025-09-19 16:16:47,587 - __main__ - INFO - 进度: 42/215
2025-09-19 16:16:47,587 - __main__ - INFO - 处理supplier: 100041
2025-09-19 16:16:47,587 - __main__ - WARNING - 未找到对应的vendor，跳过: 100041
2025-09-19 16:16:47,587 - __main__ - INFO - 进度: 43/215
2025-09-19 16:16:47,587 - __main__ - INFO - 处理supplier: 100042
2025-09-19 16:16:47,588 - __main__ - WARNING - 未找到对应的vendor，跳过: 100042
2025-09-19 16:16:47,588 - __main__ - INFO - 进度: 44/215
2025-09-19 16:16:47,588 - __main__ - INFO - 处理supplier: 100043
2025-09-19 16:16:47,588 - __main__ - WARNING - 未找到对应的vendor，跳过: 100043
2025-09-19 16:16:47,589 - __main__ - INFO - 进度: 45/215
2025-09-19 16:16:47,589 - __main__ - INFO - 处理supplier: 100044
2025-09-19 16:16:47,589 - __main__ - INFO - 找到对应vendor: 6aecfe05-3fb1-4ed0-afb8-3827d0d5452d
2025-09-19 16:16:47,590 - database_manager - INFO - 成功更新vendor: 6aecfe05-3fb1-4ed0-afb8-3827d0d5452d
2025-09-19 16:16:47,590 - __main__ - INFO - 成功更新vendor: 6aecfe05-3fb1-4ed0-afb8-3827d0d5452d
2025-09-19 16:16:47,591 - database_manager - INFO - 成功删除vendor联系方式: 6aecfe05-3fb1-4ed0-afb8-3827d0d5452d
2025-09-19 16:16:47,593 - database_manager - ERROR - 插入邮箱失败: (psycopg2.errors.NotNullViolation) null value in column "email_type" of relation "email" violates not-null constraint
DETAIL:  Failing row contains (b6d77f76-e8be-458d-82f0-102be2ddaa0c, VENDOR, 6aecfe05-3fb1-4ed0-afb8-3827d0d5452d, null, <EMAIL>, null, 2025-09-19 16:16:47.592383, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO email (
                id, entity_type, entity_id, email_type, email, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(email_type)s, %(email)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'b6d77f76-e8be-458d-82f0-102be2ddaa0c', 'entity_type': 'VENDOR', 'entity_id': UUID('6aecfe05-3fb1-4ed0-afb8-3827d0d5452d'), 'email_type': None, 'email': '<EMAIL>', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 592383), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,593 - __main__ - ERROR - 插入邮箱失败: 6aecfe05-3fb1-4ed0-afb8-3827d0d5452d
2025-09-19 16:16:47,593 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (ee865e24-9c67-4eb6-ad24-a9cb6790673b, VENDOR, 6aecfe05-3fb1-4ed0-afb8-3827d0d5452d, null, ************, null, 2025-09-19 16:16:47.593284, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'ee865e24-9c67-4eb6-ad24-a9cb6790673b', 'entity_type': 'VENDOR', 'entity_id': UUID('6aecfe05-3fb1-4ed0-afb8-3827d0d5452d'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 593284), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,594 - __main__ - ERROR - 插入电话失败: 6aecfe05-3fb1-4ed0-afb8-3827d0d5452d
2025-09-19 16:16:47,594 - __main__ - INFO - 成功处理supplier: 100044
2025-09-19 16:16:47,594 - __main__ - INFO - 进度: 46/215
2025-09-19 16:16:47,594 - __main__ - INFO - 处理supplier: 100045
2025-09-19 16:16:47,596 - __main__ - WARNING - 未找到对应的vendor，跳过: 100045
2025-09-19 16:16:47,596 - __main__ - INFO - 进度: 47/215
2025-09-19 16:16:47,596 - __main__ - INFO - 处理supplier: 100046
2025-09-19 16:16:47,599 - __main__ - WARNING - 未找到对应的vendor，跳过: 100046
2025-09-19 16:16:47,599 - __main__ - INFO - 进度: 48/215
2025-09-19 16:16:47,599 - __main__ - INFO - 处理supplier: 100047
2025-09-19 16:16:47,600 - __main__ - WARNING - 未找到对应的vendor，跳过: 100047
2025-09-19 16:16:47,600 - __main__ - INFO - 进度: 49/215
2025-09-19 16:16:47,600 - __main__ - INFO - 处理supplier: 100048
2025-09-19 16:16:47,601 - __main__ - WARNING - 未找到对应的vendor，跳过: 100048
2025-09-19 16:16:47,601 - __main__ - INFO - 进度: 50/215
2025-09-19 16:16:47,601 - __main__ - INFO - 处理supplier: 100049
2025-09-19 16:16:47,602 - __main__ - WARNING - 未找到对应的vendor，跳过: 100049
2025-09-19 16:16:47,602 - __main__ - INFO - 进度: 51/215
2025-09-19 16:16:47,602 - __main__ - INFO - 处理supplier: 100050
2025-09-19 16:16:47,603 - __main__ - WARNING - 未找到对应的vendor，跳过: 100050
2025-09-19 16:16:47,603 - __main__ - INFO - 进度: 52/215
2025-09-19 16:16:47,603 - __main__ - INFO - 处理supplier: 100051
2025-09-19 16:16:47,604 - __main__ - WARNING - 未找到对应的vendor，跳过: 100051
2025-09-19 16:16:47,604 - __main__ - INFO - 进度: 53/215
2025-09-19 16:16:47,604 - __main__ - INFO - 处理supplier: 100052
2025-09-19 16:16:47,605 - __main__ - INFO - 找到对应vendor: c05a31dc-8f1d-4b50-906c-58604d0cd307
2025-09-19 16:16:47,605 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 16:16:47,606 - database_manager - INFO - 成功更新vendor: c05a31dc-8f1d-4b50-906c-58604d0cd307
2025-09-19 16:16:47,606 - __main__ - INFO - 成功更新vendor: c05a31dc-8f1d-4b50-906c-58604d0cd307
2025-09-19 16:16:47,607 - database_manager - INFO - 成功删除vendor联系方式: c05a31dc-8f1d-4b50-906c-58604d0cd307
2025-09-19 16:16:47,609 - __main__ - INFO - 成功处理supplier: 100052
2025-09-19 16:16:47,609 - __main__ - INFO - 进度: 54/215
2025-09-19 16:16:47,609 - __main__ - INFO - 处理supplier: 100053
2025-09-19 16:16:47,610 - __main__ - WARNING - 未找到对应的vendor，跳过: 100053
2025-09-19 16:16:47,610 - __main__ - INFO - 进度: 55/215
2025-09-19 16:16:47,610 - __main__ - INFO - 处理supplier: 100054
2025-09-19 16:16:47,611 - __main__ - WARNING - 未找到对应的vendor，跳过: 100054
2025-09-19 16:16:47,611 - __main__ - INFO - 进度: 56/215
2025-09-19 16:16:47,611 - __main__ - INFO - 处理supplier: 100055
2025-09-19 16:16:47,611 - __main__ - INFO - 找到对应vendor: f133da09-8c0b-46db-9168-78135a5cc737
2025-09-19 16:16:47,612 - database_manager - INFO - 成功更新vendor: f133da09-8c0b-46db-9168-78135a5cc737
2025-09-19 16:16:47,612 - __main__ - INFO - 成功更新vendor: f133da09-8c0b-46db-9168-78135a5cc737
2025-09-19 16:16:47,613 - database_manager - INFO - 成功删除vendor联系方式: f133da09-8c0b-46db-9168-78135a5cc737
2025-09-19 16:16:47,615 - __main__ - INFO - 成功处理supplier: 100055
2025-09-19 16:16:47,615 - __main__ - INFO - 进度: 57/215
2025-09-19 16:16:47,615 - __main__ - INFO - 处理supplier: 100056
2025-09-19 16:16:47,615 - __main__ - WARNING - 未找到对应的vendor，跳过: 100056
2025-09-19 16:16:47,615 - __main__ - INFO - 进度: 58/215
2025-09-19 16:16:47,615 - __main__ - INFO - 处理supplier: 100057
2025-09-19 16:16:47,616 - __main__ - WARNING - 未找到对应的vendor，跳过: 100057
2025-09-19 16:16:47,616 - __main__ - INFO - 进度: 59/215
2025-09-19 16:16:47,616 - __main__ - INFO - 处理supplier: 100058
2025-09-19 16:16:47,616 - __main__ - WARNING - 未找到对应的vendor，跳过: 100058
2025-09-19 16:16:47,616 - __main__ - INFO - 进度: 60/215
2025-09-19 16:16:47,617 - __main__ - INFO - 处理supplier: 100059
2025-09-19 16:16:47,617 - __main__ - WARNING - 未找到对应的vendor，跳过: 100059
2025-09-19 16:16:47,617 - __main__ - INFO - 进度: 61/215
2025-09-19 16:16:47,617 - __main__ - INFO - 处理supplier: 100060
2025-09-19 16:16:47,618 - __main__ - INFO - 找到对应vendor: def22cec-77a4-4a4a-9411-810f58b23c3a
2025-09-19 16:16:47,618 - database_manager - INFO - 成功更新vendor: def22cec-77a4-4a4a-9411-810f58b23c3a
2025-09-19 16:16:47,618 - __main__ - INFO - 成功更新vendor: def22cec-77a4-4a4a-9411-810f58b23c3a
2025-09-19 16:16:47,619 - database_manager - INFO - 成功删除vendor联系方式: def22cec-77a4-4a4a-9411-810f58b23c3a
2025-09-19 16:16:47,620 - database_manager - ERROR - 插入地址失败: (psycopg2.errors.NotNullViolation) null value in column "purpose" of relation "address" violates not-null constraint
DETAIL:  Failing row contains (8ad5694d-70ed-4793-9555-a0fd27c3c094, VENDOR, def22cec-77a4-4a4a-9411-810f58b23c3a, 6333 Telegraph Rd, Commerce, CA, 90040 , USA, null, null, null, 2025-09-19 16:16:47.620336, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO address (
                id, entity_type, entity_id, street_address, city, state, 
                postal_code, country, directions, purpose, additional_lines,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(street_address)s, %(city)s, %(state)s,
                %(postal_code)s, %(country)s, %(directions)s, %(purpose)s, %(additional_lines)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '8ad5694d-70ed-4793-9555-a0fd27c3c094', 'entity_type': 'VENDOR', 'entity_id': UUID('def22cec-77a4-4a4a-9411-810f58b23c3a'), 'street_address': '6333 Telegraph Rd', 'city': 'Commerce', 'state': 'CA', 'postal_code': '90040 ', 'country': 'USA', 'directions': None, 'purpose': None, 'additional_lines': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 620336), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,621 - __main__ - ERROR - 插入地址失败: def22cec-77a4-4a4a-9411-810f58b23c3a
2025-09-19 16:16:47,624 - __main__ - INFO - 成功处理supplier: 100060
2025-09-19 16:16:47,625 - __main__ - INFO - 进度: 62/215
2025-09-19 16:16:47,625 - __main__ - INFO - 处理supplier: 100061
2025-09-19 16:16:47,625 - __main__ - WARNING - 未找到对应的vendor，跳过: 100061
2025-09-19 16:16:47,625 - __main__ - INFO - 进度: 63/215
2025-09-19 16:16:47,625 - __main__ - INFO - 处理supplier: 100062
2025-09-19 16:16:47,626 - __main__ - INFO - 找到对应vendor: 8cb7ec6d-a047-45a6-ba78-b3c99f40d67f
2025-09-19 16:16:47,626 - database_manager - INFO - 成功更新vendor: 8cb7ec6d-a047-45a6-ba78-b3c99f40d67f
2025-09-19 16:16:47,626 - __main__ - INFO - 成功更新vendor: 8cb7ec6d-a047-45a6-ba78-b3c99f40d67f
2025-09-19 16:16:47,627 - database_manager - INFO - 成功删除vendor联系方式: 8cb7ec6d-a047-45a6-ba78-b3c99f40d67f
2025-09-19 16:16:47,629 - __main__ - INFO - 成功处理supplier: 100062
2025-09-19 16:16:47,629 - __main__ - INFO - 进度: 64/215
2025-09-19 16:16:47,629 - __main__ - INFO - 处理supplier: 100063
2025-09-19 16:16:47,630 - __main__ - WARNING - 未找到对应的vendor，跳过: 100063
2025-09-19 16:16:47,630 - __main__ - INFO - 进度: 65/215
2025-09-19 16:16:47,630 - __main__ - INFO - 处理supplier: 100064
2025-09-19 16:16:47,630 - __main__ - INFO - 找到对应vendor: 8472df1c-021d-4bd0-8499-70ae338c53d1
2025-09-19 16:16:47,630 - data_mapper - WARNING - 邮箱地址无效，跳过
2025-09-19 16:16:47,631 - database_manager - INFO - 成功更新vendor: 8472df1c-021d-4bd0-8499-70ae338c53d1
2025-09-19 16:16:47,631 - __main__ - INFO - 成功更新vendor: 8472df1c-021d-4bd0-8499-70ae338c53d1
2025-09-19 16:16:47,632 - database_manager - INFO - 成功删除vendor联系方式: 8472df1c-021d-4bd0-8499-70ae338c53d1
2025-09-19 16:16:47,633 - database_manager - ERROR - 插入地址失败: (psycopg2.errors.NotNullViolation) null value in column "purpose" of relation "address" violates not-null constraint
DETAIL:  Failing row contains (9d7cb82e-770b-4cb5-9ddd-6366703ed22d, VENDOR, 8472df1c-021d-4bd0-8499-70ae338c53d1, 1975 E Locust Ave, Suite B, Ontario, CA, 91764, null, null, null, null, 2025-09-19 16:16:47.633154, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO address (
                id, entity_type, entity_id, street_address, city, state, 
                postal_code, country, directions, purpose, additional_lines,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(street_address)s, %(city)s, %(state)s,
                %(postal_code)s, %(country)s, %(directions)s, %(purpose)s, %(additional_lines)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '9d7cb82e-770b-4cb5-9ddd-6366703ed22d', 'entity_type': 'VENDOR', 'entity_id': UUID('8472df1c-021d-4bd0-8499-70ae338c53d1'), 'street_address': '1975 E Locust Ave, Suite B', 'city': 'Ontario', 'state': 'CA', 'postal_code': '91764', 'country': None, 'directions': None, 'purpose': None, 'additional_lines': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 633154), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,633 - __main__ - ERROR - 插入地址失败: 8472df1c-021d-4bd0-8499-70ae338c53d1
2025-09-19 16:16:47,636 - database_manager - ERROR - 插入邮箱失败: (psycopg2.errors.NotNullViolation) null value in column "email_type" of relation "email" violates not-null constraint
DETAIL:  Failing row contains (6f468551-5d00-4336-9ca6-f01b85c27fd0, VENDOR, 8472df1c-021d-4bd0-8499-70ae338c53d1, null, <EMAIL>, null, 2025-09-19 16:16:47.635618, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO email (
                id, entity_type, entity_id, email_type, email, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(email_type)s, %(email)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '6f468551-5d00-4336-9ca6-f01b85c27fd0', 'entity_type': 'VENDOR', 'entity_id': UUID('8472df1c-021d-4bd0-8499-70ae338c53d1'), 'email_type': None, 'email': '<EMAIL>', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 635618), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,636 - __main__ - ERROR - 插入邮箱失败: 8472df1c-021d-4bd0-8499-70ae338c53d1
2025-09-19 16:16:47,636 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (4b5a5dac-79fc-4e54-8fd7-3884cd6a7a3e, VENDOR, 8472df1c-021d-4bd0-8499-70ae338c53d1, null, ************, null, 2025-09-19 16:16:47.636369, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '4b5a5dac-79fc-4e54-8fd7-3884cd6a7a3e', 'entity_type': 'VENDOR', 'entity_id': UUID('8472df1c-021d-4bd0-8499-70ae338c53d1'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 636369), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,636 - __main__ - ERROR - 插入电话失败: 8472df1c-021d-4bd0-8499-70ae338c53d1
2025-09-19 16:16:47,637 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (77f827bc-971d-4d8a-a22f-b2611b49f022, VENDOR, 8472df1c-021d-4bd0-8499-70ae338c53d1, null, ************, null, 2025-09-19 16:16:47.637056, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '77f827bc-971d-4d8a-a22f-b2611b49f022', 'entity_type': 'VENDOR', 'entity_id': UUID('8472df1c-021d-4bd0-8499-70ae338c53d1'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 637056), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,637 - __main__ - ERROR - 插入电话失败: 8472df1c-021d-4bd0-8499-70ae338c53d1
2025-09-19 16:16:47,637 - __main__ - INFO - 成功处理supplier: 100064
2025-09-19 16:16:47,637 - __main__ - INFO - 进度: 66/215
2025-09-19 16:16:47,637 - __main__ - INFO - 处理supplier: 100065
2025-09-19 16:16:47,638 - __main__ - INFO - 找到对应vendor: c758516a-ef19-421b-8d19-c55dcf4bce5f
2025-09-19 16:16:47,638 - database_manager - INFO - 成功更新vendor: c758516a-ef19-421b-8d19-c55dcf4bce5f
2025-09-19 16:16:47,638 - __main__ - INFO - 成功更新vendor: c758516a-ef19-421b-8d19-c55dcf4bce5f
2025-09-19 16:16:47,639 - database_manager - INFO - 成功删除vendor联系方式: c758516a-ef19-421b-8d19-c55dcf4bce5f
2025-09-19 16:16:47,641 - database_manager - ERROR - 插入邮箱失败: (psycopg2.errors.NotNullViolation) null value in column "email_type" of relation "email" violates not-null constraint
DETAIL:  Failing row contains (96318e55-dba5-49a7-bb30-800e4c2d3259, VENDOR, c758516a-ef19-421b-8d19-c55dcf4bce5f, null, <EMAIL>, null, 2025-09-19 16:16:47.641095, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO email (
                id, entity_type, entity_id, email_type, email, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(email_type)s, %(email)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '96318e55-dba5-49a7-bb30-800e4c2d3259', 'entity_type': 'VENDOR', 'entity_id': UUID('c758516a-ef19-421b-8d19-c55dcf4bce5f'), 'email_type': None, 'email': '<EMAIL>', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 641095), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,641 - __main__ - ERROR - 插入邮箱失败: c758516a-ef19-421b-8d19-c55dcf4bce5f
2025-09-19 16:16:47,642 - database_manager - ERROR - 插入邮箱失败: (psycopg2.errors.NotNullViolation) null value in column "email_type" of relation "email" violates not-null constraint
DETAIL:  Failing row contains (19543f24-bd27-489c-afab-36bf4d2eef68, VENDOR, c758516a-ef19-421b-8d19-c55dcf4bce5f, null, <EMAIL>, null, 2025-09-19 16:16:47.641762, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO email (
                id, entity_type, entity_id, email_type, email, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(email_type)s, %(email)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '19543f24-bd27-489c-afab-36bf4d2eef68', 'entity_type': 'VENDOR', 'entity_id': UUID('c758516a-ef19-421b-8d19-c55dcf4bce5f'), 'email_type': None, 'email': '<EMAIL>', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 641762), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,642 - __main__ - ERROR - 插入邮箱失败: c758516a-ef19-421b-8d19-c55dcf4bce5f
2025-09-19 16:16:47,643 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (3a32b135-9814-4a72-ad92-d8ba661095ec, VENDOR, c758516a-ef19-421b-8d19-c55dcf4bce5f, null, ************, null, 2025-09-19 16:16:47.642452, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '3a32b135-9814-4a72-ad92-d8ba661095ec', 'entity_type': 'VENDOR', 'entity_id': UUID('c758516a-ef19-421b-8d19-c55dcf4bce5f'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 642452), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,643 - __main__ - ERROR - 插入电话失败: c758516a-ef19-421b-8d19-c55dcf4bce5f
2025-09-19 16:16:47,643 - __main__ - INFO - 成功处理supplier: 100065
2025-09-19 16:16:47,643 - __main__ - INFO - 进度: 67/215
2025-09-19 16:16:47,643 - __main__ - INFO - 处理supplier: 100066
2025-09-19 16:16:47,643 - __main__ - INFO - 找到对应vendor: 89e39f33-7509-4743-a4f8-ec4ee761f449
2025-09-19 16:16:47,644 - database_manager - INFO - 成功更新vendor: 89e39f33-7509-4743-a4f8-ec4ee761f449
2025-09-19 16:16:47,644 - __main__ - INFO - 成功更新vendor: 89e39f33-7509-4743-a4f8-ec4ee761f449
2025-09-19 16:16:47,645 - database_manager - INFO - 成功删除vendor联系方式: 89e39f33-7509-4743-a4f8-ec4ee761f449
2025-09-19 16:16:47,646 - database_manager - ERROR - 插入邮箱失败: (psycopg2.errors.NotNullViolation) null value in column "email_type" of relation "email" violates not-null constraint
DETAIL:  Failing row contains (4aaec2c8-d53e-423f-8543-84bb0204ae6a, VENDOR, 89e39f33-7509-4743-a4f8-ec4ee761f449, null, <EMAIL>, null, 2025-09-19 16:16:47.646029, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO email (
                id, entity_type, entity_id, email_type, email, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(email_type)s, %(email)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '4aaec2c8-d53e-423f-8543-84bb0204ae6a', 'entity_type': 'VENDOR', 'entity_id': UUID('89e39f33-7509-4743-a4f8-ec4ee761f449'), 'email_type': None, 'email': '<EMAIL>', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 646029), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,646 - __main__ - ERROR - 插入邮箱失败: 89e39f33-7509-4743-a4f8-ec4ee761f449
2025-09-19 16:16:47,647 - database_manager - ERROR - 插入邮箱失败: (psycopg2.errors.NotNullViolation) null value in column "email_type" of relation "email" violates not-null constraint
DETAIL:  Failing row contains (2e2c5887-97d4-494b-9008-652a7510f707, VENDOR, 89e39f33-7509-4743-a4f8-ec4ee761f449, null, <EMAIL>, null, 2025-09-19 16:16:47.646691, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO email (
                id, entity_type, entity_id, email_type, email, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(email_type)s, %(email)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '2e2c5887-97d4-494b-9008-652a7510f707', 'entity_type': 'VENDOR', 'entity_id': UUID('89e39f33-7509-4743-a4f8-ec4ee761f449'), 'email_type': None, 'email': '<EMAIL>', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 646691), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,647 - __main__ - ERROR - 插入邮箱失败: 89e39f33-7509-4743-a4f8-ec4ee761f449
2025-09-19 16:16:47,647 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (2f7d8b51-f868-470c-9f17-db4b207833c0, VENDOR, 89e39f33-7509-4743-a4f8-ec4ee761f449, null, ************, null, 2025-09-19 16:16:47.647378, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '2f7d8b51-f868-470c-9f17-db4b207833c0', 'entity_type': 'VENDOR', 'entity_id': UUID('89e39f33-7509-4743-a4f8-ec4ee761f449'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 647378), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,647 - __main__ - ERROR - 插入电话失败: 89e39f33-7509-4743-a4f8-ec4ee761f449
2025-09-19 16:16:47,648 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (c8bf9176-5b02-4ff2-843e-492c8f30c316, VENDOR, 89e39f33-7509-4743-a4f8-ec4ee761f449, null, ************, null, 2025-09-19 16:16:47.648053, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'c8bf9176-5b02-4ff2-843e-492c8f30c316', 'entity_type': 'VENDOR', 'entity_id': UUID('89e39f33-7509-4743-a4f8-ec4ee761f449'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 648053), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,648 - __main__ - ERROR - 插入电话失败: 89e39f33-7509-4743-a4f8-ec4ee761f449
2025-09-19 16:16:47,648 - __main__ - INFO - 成功处理supplier: 100066
2025-09-19 16:16:47,648 - __main__ - INFO - 进度: 68/215
2025-09-19 16:16:47,648 - __main__ - INFO - 处理supplier: 100067
2025-09-19 16:16:47,649 - __main__ - INFO - 找到对应vendor: 61fb4a48-b14a-455a-b7ca-0761ddc1542e
2025-09-19 16:16:47,650 - database_manager - INFO - 成功更新vendor: 61fb4a48-b14a-455a-b7ca-0761ddc1542e
2025-09-19 16:16:47,650 - __main__ - INFO - 成功更新vendor: 61fb4a48-b14a-455a-b7ca-0761ddc1542e
2025-09-19 16:16:47,651 - database_manager - INFO - 成功删除vendor联系方式: 61fb4a48-b14a-455a-b7ca-0761ddc1542e
2025-09-19 16:16:47,652 - database_manager - ERROR - 插入地址失败: (psycopg2.errors.NotNullViolation) null value in column "purpose" of relation "address" violates not-null constraint
DETAIL:  Failing row contains (e8fc4f3d-f7b4-4a3a-b890-2083473be024, VENDOR, 61fb4a48-b14a-455a-b7ca-0761ddc1542e, 6370 Altura Blvd., Buena Park, CA, 90620, null, null, null, null, 2025-09-19 16:16:47.651906, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO address (
                id, entity_type, entity_id, street_address, city, state, 
                postal_code, country, directions, purpose, additional_lines,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(street_address)s, %(city)s, %(state)s,
                %(postal_code)s, %(country)s, %(directions)s, %(purpose)s, %(additional_lines)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'e8fc4f3d-f7b4-4a3a-b890-2083473be024', 'entity_type': 'VENDOR', 'entity_id': UUID('61fb4a48-b14a-455a-b7ca-0761ddc1542e'), 'street_address': '6370 Altura Blvd.', 'city': 'Buena Park', 'state': 'CA', 'postal_code': '90620', 'country': None, 'directions': None, 'purpose': None, 'additional_lines': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 651906), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,652 - __main__ - ERROR - 插入地址失败: 61fb4a48-b14a-455a-b7ca-0761ddc1542e
2025-09-19 16:16:47,653 - database_manager - ERROR - 插入邮箱失败: (psycopg2.errors.NotNullViolation) null value in column "email_type" of relation "email" violates not-null constraint
DETAIL:  Failing row contains (36840832-8d65-4577-bde8-ca7c76e8e6db, VENDOR, 61fb4a48-b14a-455a-b7ca-0761ddc1542e, null, <EMAIL>, null, 2025-09-19 16:16:47.653227, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO email (
                id, entity_type, entity_id, email_type, email, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(email_type)s, %(email)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '36840832-8d65-4577-bde8-ca7c76e8e6db', 'entity_type': 'VENDOR', 'entity_id': UUID('61fb4a48-b14a-455a-b7ca-0761ddc1542e'), 'email_type': None, 'email': '<EMAIL>', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 653227), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,653 - __main__ - ERROR - 插入邮箱失败: 61fb4a48-b14a-455a-b7ca-0761ddc1542e
2025-09-19 16:16:47,654 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (ad34214a-b82a-4a77-b92c-46e36360238f, VENDOR, 61fb4a48-b14a-455a-b7ca-0761ddc1542e, null, (*************, null, 2025-09-19 16:16:47.653878, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'ad34214a-b82a-4a77-b92c-46e36360238f', 'entity_type': 'VENDOR', 'entity_id': UUID('61fb4a48-b14a-455a-b7ca-0761ddc1542e'), 'phone_type': None, 'phone_number': '(*************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 653878), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,654 - __main__ - ERROR - 插入电话失败: 61fb4a48-b14a-455a-b7ca-0761ddc1542e
2025-09-19 16:16:47,655 - database_manager - ERROR - 插入网址失败: (psycopg2.errors.NotNullViolation) null value in column "web_address_type" of relation "web_address" violates not-null constraint
DETAIL:  Failing row contains (cd0a0af0-7b6d-4d9c-b792-26eac37877a9, VENDOR, 61fb4a48-b14a-455a-b7ca-0761ddc1542e, null, https://www.lastotallyawesome.com/, 2025-09-19 16:16:47.654541, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO web_address (
                id, entity_type, entity_id, web_address_type, web_address,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(web_address_type)s, %(web_address)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'cd0a0af0-7b6d-4d9c-b792-26eac37877a9', 'entity_type': 'VENDOR', 'entity_id': UUID('61fb4a48-b14a-455a-b7ca-0761ddc1542e'), 'web_address_type': None, 'web_address': 'https://www.lastotallyawesome.com/', 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 654541), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,655 - __main__ - ERROR - 插入网址失败: 61fb4a48-b14a-455a-b7ca-0761ddc1542e
2025-09-19 16:16:47,655 - __main__ - INFO - 成功处理supplier: 100067
2025-09-19 16:16:47,655 - __main__ - INFO - 进度: 69/215
2025-09-19 16:16:47,655 - __main__ - INFO - 处理supplier: 100068
2025-09-19 16:16:47,655 - __main__ - INFO - 找到对应vendor: af7e1257-0990-43d4-b61c-52405af59c19
2025-09-19 16:16:47,656 - database_manager - INFO - 成功更新vendor: af7e1257-0990-43d4-b61c-52405af59c19
2025-09-19 16:16:47,656 - __main__ - INFO - 成功更新vendor: af7e1257-0990-43d4-b61c-52405af59c19
2025-09-19 16:16:47,657 - database_manager - INFO - 成功删除vendor联系方式: af7e1257-0990-43d4-b61c-52405af59c19
2025-09-19 16:16:47,658 - database_manager - ERROR - 插入地址失败: (psycopg2.errors.NotNullViolation) null value in column "purpose" of relation "address" violates not-null constraint
DETAIL:  Failing row contains (c47e925e-3f81-4f08-8474-7a4d6f307cfe, VENDOR, af7e1257-0990-43d4-b61c-52405af59c19, 7046 Jackson Street, Paramount, CA, 90723, null, null, null, null, 2025-09-19 16:16:47.657979, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO address (
                id, entity_type, entity_id, street_address, city, state, 
                postal_code, country, directions, purpose, additional_lines,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(street_address)s, %(city)s, %(state)s,
                %(postal_code)s, %(country)s, %(directions)s, %(purpose)s, %(additional_lines)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'c47e925e-3f81-4f08-8474-7a4d6f307cfe', 'entity_type': 'VENDOR', 'entity_id': UUID('af7e1257-0990-43d4-b61c-52405af59c19'), 'street_address': '7046 Jackson Street', 'city': 'Paramount', 'state': 'CA', 'postal_code': '90723', 'country': None, 'directions': None, 'purpose': None, 'additional_lines': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 657979), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,658 - __main__ - ERROR - 插入地址失败: af7e1257-0990-43d4-b61c-52405af59c19
2025-09-19 16:16:47,659 - __main__ - INFO - 成功处理supplier: 100068
2025-09-19 16:16:47,659 - __main__ - INFO - 进度: 70/215
2025-09-19 16:16:47,659 - __main__ - INFO - 处理supplier: 100069
2025-09-19 16:16:47,660 - __main__ - INFO - 找到对应vendor: 23acd2ff-09b4-43b2-b345-c1ac11674123
2025-09-19 16:16:47,660 - database_manager - INFO - 成功更新vendor: 23acd2ff-09b4-43b2-b345-c1ac11674123
2025-09-19 16:16:47,661 - __main__ - INFO - 成功更新vendor: 23acd2ff-09b4-43b2-b345-c1ac11674123
2025-09-19 16:16:47,662 - database_manager - INFO - 成功删除vendor联系方式: 23acd2ff-09b4-43b2-b345-c1ac11674123
2025-09-19 16:16:47,663 - database_manager - ERROR - 插入地址失败: (psycopg2.errors.NotNullViolation) null value in column "purpose" of relation "address" violates not-null constraint
DETAIL:  Failing row contains (1cc0c600-cedd-4094-9f41-655a969f0ddc, VENDOR, 23acd2ff-09b4-43b2-b345-c1ac11674123, 7803 Telegraph Rd, Montebello, CA, 90640, null, null, null, null, 2025-09-19 16:16:47.66273, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO address (
                id, entity_type, entity_id, street_address, city, state, 
                postal_code, country, directions, purpose, additional_lines,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(street_address)s, %(city)s, %(state)s,
                %(postal_code)s, %(country)s, %(directions)s, %(purpose)s, %(additional_lines)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '1cc0c600-cedd-4094-9f41-655a969f0ddc', 'entity_type': 'VENDOR', 'entity_id': UUID('23acd2ff-09b4-43b2-b345-c1ac11674123'), 'street_address': '7803 Telegraph Rd', 'city': 'Montebello', 'state': 'CA', 'postal_code': '90640', 'country': None, 'directions': None, 'purpose': None, 'additional_lines': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 662730), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,663 - __main__ - ERROR - 插入地址失败: 23acd2ff-09b4-43b2-b345-c1ac11674123
2025-09-19 16:16:47,666 - database_manager - ERROR - 插入网址失败: (psycopg2.errors.NotNullViolation) null value in column "web_address_type" of relation "web_address" violates not-null constraint
DETAIL:  Failing row contains (4ebbc63e-84ee-490a-8d0b-7b8f56f74089, VENDOR, 23acd2ff-09b4-43b2-b345-c1ac11674123, null, https://www.bajamicheladas.com/, 2025-09-19 16:16:47.665837, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO web_address (
                id, entity_type, entity_id, web_address_type, web_address,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(web_address_type)s, %(web_address)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '4ebbc63e-84ee-490a-8d0b-7b8f56f74089', 'entity_type': 'VENDOR', 'entity_id': UUID('23acd2ff-09b4-43b2-b345-c1ac11674123'), 'web_address_type': None, 'web_address': 'https://www.bajamicheladas.com/', 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 665837), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,666 - __main__ - ERROR - 插入网址失败: 23acd2ff-09b4-43b2-b345-c1ac11674123
2025-09-19 16:16:47,666 - __main__ - INFO - 成功处理supplier: 100069
2025-09-19 16:16:47,666 - __main__ - INFO - 进度: 71/215
2025-09-19 16:16:47,666 - __main__ - INFO - 处理supplier: 100070
2025-09-19 16:16:47,667 - __main__ - WARNING - 未找到对应的vendor，跳过: 100070
2025-09-19 16:16:47,667 - __main__ - INFO - 进度: 72/215
2025-09-19 16:16:47,667 - __main__ - INFO - 处理supplier: 100071
2025-09-19 16:16:47,667 - __main__ - WARNING - 未找到对应的vendor，跳过: 100071
2025-09-19 16:16:47,667 - __main__ - INFO - 进度: 73/215
2025-09-19 16:16:47,667 - __main__ - INFO - 处理supplier: 100072
2025-09-19 16:16:47,668 - __main__ - WARNING - 未找到对应的vendor，跳过: 100072
2025-09-19 16:16:47,668 - __main__ - INFO - 进度: 74/215
2025-09-19 16:16:47,668 - __main__ - INFO - 处理supplier: 100073
2025-09-19 16:16:47,668 - __main__ - INFO - 找到对应vendor: 39ec865b-ba20-49fa-baa6-a41589ee5d8a
2025-09-19 16:16:47,669 - database_manager - INFO - 成功更新vendor: 39ec865b-ba20-49fa-baa6-a41589ee5d8a
2025-09-19 16:16:47,669 - __main__ - INFO - 成功更新vendor: 39ec865b-ba20-49fa-baa6-a41589ee5d8a
2025-09-19 16:16:47,670 - database_manager - INFO - 成功删除vendor联系方式: 39ec865b-ba20-49fa-baa6-a41589ee5d8a
2025-09-19 16:16:47,673 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (d7952f36-337c-4866-8dd1-5a69b0e1dfd5, VENDOR, 39ec865b-ba20-49fa-baa6-a41589ee5d8a, null, ************, null, 2025-09-19 16:16:47.673236, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'd7952f36-337c-4866-8dd1-5a69b0e1dfd5', 'entity_type': 'VENDOR', 'entity_id': UUID('39ec865b-ba20-49fa-baa6-a41589ee5d8a'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 673236), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,673 - __main__ - ERROR - 插入电话失败: 39ec865b-ba20-49fa-baa6-a41589ee5d8a
2025-09-19 16:16:47,674 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (9a9af91c-6d75-4395-bacd-00dab4fb26a2, VENDOR, 39ec865b-ba20-49fa-baa6-a41589ee5d8a, null, ************, null, 2025-09-19 16:16:47.673886, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '9a9af91c-6d75-4395-bacd-00dab4fb26a2', 'entity_type': 'VENDOR', 'entity_id': UUID('39ec865b-ba20-49fa-baa6-a41589ee5d8a'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 673886), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,674 - __main__ - ERROR - 插入电话失败: 39ec865b-ba20-49fa-baa6-a41589ee5d8a
2025-09-19 16:16:47,675 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (fa89bcf0-1d7d-4c95-803d-52c64608e1cb, VENDOR, 39ec865b-ba20-49fa-baa6-a41589ee5d8a, null, ************, null, 2025-09-19 16:16:47.67452, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'fa89bcf0-1d7d-4c95-803d-52c64608e1cb', 'entity_type': 'VENDOR', 'entity_id': UUID('39ec865b-ba20-49fa-baa6-a41589ee5d8a'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 674520), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,675 - __main__ - ERROR - 插入电话失败: 39ec865b-ba20-49fa-baa6-a41589ee5d8a
2025-09-19 16:16:47,675 - __main__ - INFO - 成功处理supplier: 100073
2025-09-19 16:16:47,675 - __main__ - INFO - 进度: 75/215
2025-09-19 16:16:47,675 - __main__ - INFO - 处理supplier: 100074
2025-09-19 16:16:47,675 - __main__ - INFO - 找到对应vendor: d22d7073-1ea0-4db6-b72d-d37cfddfccc8
2025-09-19 16:16:47,676 - database_manager - INFO - 成功更新vendor: d22d7073-1ea0-4db6-b72d-d37cfddfccc8
2025-09-19 16:16:47,676 - __main__ - INFO - 成功更新vendor: d22d7073-1ea0-4db6-b72d-d37cfddfccc8
2025-09-19 16:16:47,677 - database_manager - INFO - 成功删除vendor联系方式: d22d7073-1ea0-4db6-b72d-d37cfddfccc8
2025-09-19 16:16:47,679 - database_manager - ERROR - 插入邮箱失败: (psycopg2.errors.NotNullViolation) null value in column "email_type" of relation "email" violates not-null constraint
DETAIL:  Failing row contains (5a5fb5c0-6fdc-4672-8dd0-7200e1b96933, VENDOR, d22d7073-1ea0-4db6-b72d-d37cfddfccc8, null, <EMAIL>, null, 2025-09-19 16:16:47.67859, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO email (
                id, entity_type, entity_id, email_type, email, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(email_type)s, %(email)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '5a5fb5c0-6fdc-4672-8dd0-7200e1b96933', 'entity_type': 'VENDOR', 'entity_id': UUID('d22d7073-1ea0-4db6-b72d-d37cfddfccc8'), 'email_type': None, 'email': '<EMAIL>', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 678590), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,679 - __main__ - ERROR - 插入邮箱失败: d22d7073-1ea0-4db6-b72d-d37cfddfccc8
2025-09-19 16:16:47,679 - __main__ - INFO - 成功处理supplier: 100074
2025-09-19 16:16:47,679 - __main__ - INFO - 进度: 76/215
2025-09-19 16:16:47,679 - __main__ - INFO - 处理supplier: 100075
2025-09-19 16:16:47,679 - __main__ - INFO - 找到对应vendor: e07a8ae6-eda7-4926-82f8-fb90493cf5ae
2025-09-19 16:16:47,680 - database_manager - INFO - 成功更新vendor: e07a8ae6-eda7-4926-82f8-fb90493cf5ae
2025-09-19 16:16:47,680 - __main__ - INFO - 成功更新vendor: e07a8ae6-eda7-4926-82f8-fb90493cf5ae
2025-09-19 16:16:47,681 - database_manager - INFO - 成功删除vendor联系方式: e07a8ae6-eda7-4926-82f8-fb90493cf5ae
2025-09-19 16:16:47,682 - __main__ - INFO - 成功处理supplier: 100075
2025-09-19 16:16:47,682 - __main__ - INFO - 进度: 77/215
2025-09-19 16:16:47,682 - __main__ - INFO - 处理supplier: 100076
2025-09-19 16:16:47,682 - __main__ - WARNING - 未找到对应的vendor，跳过: 100076
2025-09-19 16:16:47,682 - __main__ - INFO - 进度: 78/215
2025-09-19 16:16:47,682 - __main__ - INFO - 处理supplier: 100077
2025-09-19 16:16:47,683 - __main__ - WARNING - 未找到对应的vendor，跳过: 100077
2025-09-19 16:16:47,683 - __main__ - INFO - 进度: 79/215
2025-09-19 16:16:47,683 - __main__ - INFO - 处理supplier: 100078
2025-09-19 16:16:47,683 - __main__ - WARNING - 未找到对应的vendor，跳过: 100078
2025-09-19 16:16:47,683 - __main__ - INFO - 进度: 80/215
2025-09-19 16:16:47,683 - __main__ - INFO - 处理supplier: 100079
2025-09-19 16:16:47,684 - __main__ - INFO - 找到对应vendor: aff88d19-cfb0-41db-8804-48dc9dde98ce
2025-09-19 16:16:47,684 - database_manager - INFO - 成功更新vendor: aff88d19-cfb0-41db-8804-48dc9dde98ce
2025-09-19 16:16:47,684 - __main__ - INFO - 成功更新vendor: aff88d19-cfb0-41db-8804-48dc9dde98ce
2025-09-19 16:16:47,685 - database_manager - INFO - 成功删除vendor联系方式: aff88d19-cfb0-41db-8804-48dc9dde98ce
2025-09-19 16:16:47,687 - database_manager - ERROR - 插入地址失败: (psycopg2.errors.NotNullViolation) null value in column "purpose" of relation "address" violates not-null constraint
DETAIL:  Failing row contains (8bda178f-344a-4f21-b505-d39bacc1053a, VENDOR, aff88d19-cfb0-41db-8804-48dc9dde98ce, 1972 E 20th St, Los Angeles, CA, 90058, USA, null, null, null, 2025-09-19 16:16:47.686584, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO address (
                id, entity_type, entity_id, street_address, city, state, 
                postal_code, country, directions, purpose, additional_lines,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(street_address)s, %(city)s, %(state)s,
                %(postal_code)s, %(country)s, %(directions)s, %(purpose)s, %(additional_lines)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '8bda178f-344a-4f21-b505-d39bacc1053a', 'entity_type': 'VENDOR', 'entity_id': UUID('aff88d19-cfb0-41db-8804-48dc9dde98ce'), 'street_address': '1972 E 20th St', 'city': 'Los Angeles', 'state': 'CA', 'postal_code': '90058', 'country': 'USA', 'directions': None, 'purpose': None, 'additional_lines': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 686584), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,687 - __main__ - ERROR - 插入地址失败: aff88d19-cfb0-41db-8804-48dc9dde98ce
2025-09-19 16:16:47,689 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (716ccdf0-29f1-47ef-ad6b-b969ee65d2ba, VENDOR, aff88d19-cfb0-41db-8804-48dc9dde98ce, null, ************, null, 2025-09-19 16:16:47.689131, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '716ccdf0-29f1-47ef-ad6b-b969ee65d2ba', 'entity_type': 'VENDOR', 'entity_id': UUID('aff88d19-cfb0-41db-8804-48dc9dde98ce'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 689131), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,689 - __main__ - ERROR - 插入电话失败: aff88d19-cfb0-41db-8804-48dc9dde98ce
2025-09-19 16:16:47,690 - database_manager - ERROR - 插入网址失败: (psycopg2.errors.NotNullViolation) null value in column "web_address_type" of relation "web_address" violates not-null constraint
DETAIL:  Failing row contains (2cd81eed-f6fb-4f11-8bfe-6e07a338839d, VENDOR, aff88d19-cfb0-41db-8804-48dc9dde98ce, null, https://shurepreint.b2bwave.com/, 2025-09-19 16:16:47.689813, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO web_address (
                id, entity_type, entity_id, web_address_type, web_address,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(web_address_type)s, %(web_address)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '2cd81eed-f6fb-4f11-8bfe-6e07a338839d', 'entity_type': 'VENDOR', 'entity_id': UUID('aff88d19-cfb0-41db-8804-48dc9dde98ce'), 'web_address_type': None, 'web_address': 'https://shurepreint.b2bwave.com/', 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 689813), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,690 - __main__ - ERROR - 插入网址失败: aff88d19-cfb0-41db-8804-48dc9dde98ce
2025-09-19 16:16:47,690 - __main__ - INFO - 成功处理supplier: 100079
2025-09-19 16:16:47,690 - __main__ - INFO - 进度: 81/215
2025-09-19 16:16:47,690 - __main__ - INFO - 处理supplier: 100080
2025-09-19 16:16:47,691 - __main__ - INFO - 找到对应vendor: dde584c3-5356-4ba7-bd37-b3ddc92b8b7d
2025-09-19 16:16:47,691 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 16:16:47,691 - database_manager - INFO - 成功更新vendor: dde584c3-5356-4ba7-bd37-b3ddc92b8b7d
2025-09-19 16:16:47,691 - __main__ - INFO - 成功更新vendor: dde584c3-5356-4ba7-bd37-b3ddc92b8b7d
2025-09-19 16:16:47,692 - database_manager - INFO - 成功删除vendor联系方式: dde584c3-5356-4ba7-bd37-b3ddc92b8b7d
2025-09-19 16:16:47,694 - database_manager - ERROR - 插入邮箱失败: (psycopg2.errors.NotNullViolation) null value in column "email_type" of relation "email" violates not-null constraint
DETAIL:  Failing row contains (236a2004-21db-4aba-af5d-bbd803ee2e61, VENDOR, dde584c3-5356-4ba7-bd37-b3ddc92b8b7d, null, <EMAIL>, null, 2025-09-19 16:16:47.693391, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO email (
                id, entity_type, entity_id, email_type, email, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(email_type)s, %(email)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '236a2004-21db-4aba-af5d-bbd803ee2e61', 'entity_type': 'VENDOR', 'entity_id': UUID('dde584c3-5356-4ba7-bd37-b3ddc92b8b7d'), 'email_type': None, 'email': '<EMAIL>', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 693391), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,694 - __main__ - ERROR - 插入邮箱失败: dde584c3-5356-4ba7-bd37-b3ddc92b8b7d
2025-09-19 16:16:47,694 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (72d1d072-ce99-488d-bb4e-a4bce59c7273, VENDOR, dde584c3-5356-4ba7-bd37-b3ddc92b8b7d, null, ************, null, 2025-09-19 16:16:47.694179, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '72d1d072-ce99-488d-bb4e-a4bce59c7273', 'entity_type': 'VENDOR', 'entity_id': UUID('dde584c3-5356-4ba7-bd37-b3ddc92b8b7d'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 694179), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,694 - __main__ - ERROR - 插入电话失败: dde584c3-5356-4ba7-bd37-b3ddc92b8b7d
2025-09-19 16:16:47,695 - __main__ - INFO - 成功处理supplier: 100080
2025-09-19 16:16:47,695 - __main__ - INFO - 进度: 82/215
2025-09-19 16:16:47,695 - __main__ - INFO - 处理supplier: 100081
2025-09-19 16:16:47,695 - __main__ - WARNING - 未找到对应的vendor，跳过: 100081
2025-09-19 16:16:47,695 - __main__ - INFO - 进度: 83/215
2025-09-19 16:16:47,695 - __main__ - INFO - 处理supplier: 100082
2025-09-19 16:16:47,696 - __main__ - INFO - 找到对应vendor: e2b00391-ae40-44dd-b563-2bec55d8c8be
2025-09-19 16:16:47,696 - database_manager - INFO - 成功更新vendor: e2b00391-ae40-44dd-b563-2bec55d8c8be
2025-09-19 16:16:47,697 - __main__ - INFO - 成功更新vendor: e2b00391-ae40-44dd-b563-2bec55d8c8be
2025-09-19 16:16:47,698 - database_manager - INFO - 成功删除vendor联系方式: e2b00391-ae40-44dd-b563-2bec55d8c8be
2025-09-19 16:16:47,702 - __main__ - INFO - 成功处理supplier: 100082
2025-09-19 16:16:47,702 - __main__ - INFO - 进度: 84/215
2025-09-19 16:16:47,702 - __main__ - INFO - 处理supplier: 100083
2025-09-19 16:16:47,703 - __main__ - WARNING - 未找到对应的vendor，跳过: 100083
2025-09-19 16:16:47,703 - __main__ - INFO - 进度: 85/215
2025-09-19 16:16:47,703 - __main__ - INFO - 处理supplier: 100084
2025-09-19 16:16:47,703 - __main__ - INFO - 找到对应vendor: e4ebc7b0-c2b6-40ff-b1ee-35f0e6cb63f0
2025-09-19 16:16:47,704 - database_manager - INFO - 成功更新vendor: e4ebc7b0-c2b6-40ff-b1ee-35f0e6cb63f0
2025-09-19 16:16:47,704 - __main__ - INFO - 成功更新vendor: e4ebc7b0-c2b6-40ff-b1ee-35f0e6cb63f0
2025-09-19 16:16:47,705 - database_manager - INFO - 成功删除vendor联系方式: e4ebc7b0-c2b6-40ff-b1ee-35f0e6cb63f0
2025-09-19 16:16:47,708 - database_manager - ERROR - 插入地址失败: (psycopg2.errors.NotNullViolation) null value in column "purpose" of relation "address" violates not-null constraint
DETAIL:  Failing row contains (79fa0d59-28fe-49bb-a2dd-3e048f478877, VENDOR, e4ebc7b0-c2b6-40ff-b1ee-35f0e6cb63f0, 12238 BELL RANCH DR, SANTA FE SPRINGS, CA, 90670, USA, null, null, null, 2025-09-19 16:16:47.707057, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO address (
                id, entity_type, entity_id, street_address, city, state, 
                postal_code, country, directions, purpose, additional_lines,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(street_address)s, %(city)s, %(state)s,
                %(postal_code)s, %(country)s, %(directions)s, %(purpose)s, %(additional_lines)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '79fa0d59-28fe-49bb-a2dd-3e048f478877', 'entity_type': 'VENDOR', 'entity_id': UUID('e4ebc7b0-c2b6-40ff-b1ee-35f0e6cb63f0'), 'street_address': '12238 BELL RANCH DR', 'city': 'SANTA FE SPRINGS', 'state': 'CA', 'postal_code': '90670', 'country': 'USA', 'directions': None, 'purpose': None, 'additional_lines': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 707057), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,708 - __main__ - ERROR - 插入地址失败: e4ebc7b0-c2b6-40ff-b1ee-35f0e6cb63f0
2025-09-19 16:16:47,713 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (2a46ce8f-fc40-4731-bc04-2be242f6d3d4, VENDOR, e4ebc7b0-c2b6-40ff-b1ee-35f0e6cb63f0, null, (*************, null, 2025-09-19 16:16:47.713119, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '2a46ce8f-fc40-4731-bc04-2be242f6d3d4', 'entity_type': 'VENDOR', 'entity_id': UUID('e4ebc7b0-c2b6-40ff-b1ee-35f0e6cb63f0'), 'phone_type': None, 'phone_number': '(*************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 713119), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,713 - __main__ - ERROR - 插入电话失败: e4ebc7b0-c2b6-40ff-b1ee-35f0e6cb63f0
2025-09-19 16:16:47,714 - __main__ - INFO - 成功处理supplier: 100084
2025-09-19 16:16:47,714 - __main__ - INFO - 进度: 86/215
2025-09-19 16:16:47,714 - __main__ - INFO - 处理supplier: 100085
2025-09-19 16:16:47,715 - __main__ - INFO - 找到对应vendor: 697f79d3-2092-4e1c-a3ff-b2fe07831fd8
2025-09-19 16:16:47,715 - database_manager - INFO - 成功更新vendor: 697f79d3-2092-4e1c-a3ff-b2fe07831fd8
2025-09-19 16:16:47,716 - __main__ - INFO - 成功更新vendor: 697f79d3-2092-4e1c-a3ff-b2fe07831fd8
2025-09-19 16:16:47,717 - database_manager - INFO - 成功删除vendor联系方式: 697f79d3-2092-4e1c-a3ff-b2fe07831fd8
2025-09-19 16:16:47,719 - database_manager - ERROR - 插入地址失败: (psycopg2.errors.NotNullViolation) null value in column "purpose" of relation "address" violates not-null constraint
DETAIL:  Failing row contains (0dc7f8c5-239d-4004-9542-68c7b798354d, VENDOR, 697f79d3-2092-4e1c-a3ff-b2fe07831fd8, 2141 S Dupont Drive, Anaheim, CA, 92806, USA, null, null, ************ , 2025-09-19 16:16:47.718952, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO address (
                id, entity_type, entity_id, street_address, city, state, 
                postal_code, country, directions, purpose, additional_lines,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(street_address)s, %(city)s, %(state)s,
                %(postal_code)s, %(country)s, %(directions)s, %(purpose)s, %(additional_lines)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '0dc7f8c5-239d-4004-9542-68c7b798354d', 'entity_type': 'VENDOR', 'entity_id': UUID('697f79d3-2092-4e1c-a3ff-b2fe07831fd8'), 'street_address': '2141 S Dupont Drive', 'city': 'Anaheim', 'state': 'CA', 'postal_code': '92806', 'country': 'USA', 'directions': None, 'purpose': None, 'additional_lines': '************ ', 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 718952), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,719 - __main__ - ERROR - 插入地址失败: 697f79d3-2092-4e1c-a3ff-b2fe07831fd8
2025-09-19 16:16:47,721 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (f9953e1c-6a98-4d01-912c-dad77224ea8e, VENDOR, 697f79d3-2092-4e1c-a3ff-b2fe07831fd8, null, ************ , null, 2025-09-19 16:16:47.720733, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'f9953e1c-6a98-4d01-912c-dad77224ea8e', 'entity_type': 'VENDOR', 'entity_id': UUID('697f79d3-2092-4e1c-a3ff-b2fe07831fd8'), 'phone_type': None, 'phone_number': '************ ', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 720733), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,721 - __main__ - ERROR - 插入电话失败: 697f79d3-2092-4e1c-a3ff-b2fe07831fd8
2025-09-19 16:16:47,722 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (a154b747-fade-42c2-9654-0afc784a0e92, VENDOR, 697f79d3-2092-4e1c-a3ff-b2fe07831fd8, null, ************ , null, 2025-09-19 16:16:47.721501, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'a154b747-fade-42c2-9654-0afc784a0e92', 'entity_type': 'VENDOR', 'entity_id': UUID('697f79d3-2092-4e1c-a3ff-b2fe07831fd8'), 'phone_type': None, 'phone_number': '************ ', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 721501), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,722 - __main__ - ERROR - 插入电话失败: 697f79d3-2092-4e1c-a3ff-b2fe07831fd8
2025-09-19 16:16:47,722 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (e72f4018-aa67-4349-af1b-9556126f4825, VENDOR, 697f79d3-2092-4e1c-a3ff-b2fe07831fd8, null, (714)363-3899, null, 2025-09-19 16:16:47.722243, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'e72f4018-aa67-4349-af1b-9556126f4825', 'entity_type': 'VENDOR', 'entity_id': UUID('697f79d3-2092-4e1c-a3ff-b2fe07831fd8'), 'phone_type': None, 'phone_number': '(714)363-3899', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 722243), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,722 - __main__ - ERROR - 插入电话失败: 697f79d3-2092-4e1c-a3ff-b2fe07831fd8
2025-09-19 16:16:47,723 - database_manager - ERROR - 插入网址失败: (psycopg2.errors.NotNullViolation) null value in column "web_address_type" of relation "web_address" violates not-null constraint
DETAIL:  Failing row contains (936f021b-182a-408d-be8b-3d4ad6f4e0df, VENDOR, 697f79d3-2092-4e1c-a3ff-b2fe07831fd8, null, www.RippedJerky.com, 2025-09-19 16:16:47.722959, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO web_address (
                id, entity_type, entity_id, web_address_type, web_address,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(web_address_type)s, %(web_address)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '936f021b-182a-408d-be8b-3d4ad6f4e0df', 'entity_type': 'VENDOR', 'entity_id': UUID('697f79d3-2092-4e1c-a3ff-b2fe07831fd8'), 'web_address_type': None, 'web_address': 'www.RippedJerky.com', 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 722959), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,723 - __main__ - ERROR - 插入网址失败: 697f79d3-2092-4e1c-a3ff-b2fe07831fd8
2025-09-19 16:16:47,723 - __main__ - INFO - 成功处理supplier: 100085
2025-09-19 16:16:47,723 - __main__ - INFO - 进度: 87/215
2025-09-19 16:16:47,723 - __main__ - INFO - 处理supplier: 100086
2025-09-19 16:16:47,724 - __main__ - INFO - 找到对应vendor: 2ee01f74-7100-4b20-a36e-c72bdeb3031f
2025-09-19 16:16:47,725 - database_manager - INFO - 成功更新vendor: 2ee01f74-7100-4b20-a36e-c72bdeb3031f
2025-09-19 16:16:47,725 - __main__ - INFO - 成功更新vendor: 2ee01f74-7100-4b20-a36e-c72bdeb3031f
2025-09-19 16:16:47,726 - database_manager - INFO - 成功删除vendor联系方式: 2ee01f74-7100-4b20-a36e-c72bdeb3031f
2025-09-19 16:16:47,727 - __main__ - INFO - 成功处理supplier: 100086
2025-09-19 16:16:47,727 - __main__ - INFO - 进度: 88/215
2025-09-19 16:16:47,727 - __main__ - INFO - 处理supplier: 100087
2025-09-19 16:16:47,728 - __main__ - INFO - 找到对应vendor: fe8fcd49-ea3d-43a8-b8fb-3b3429986603
2025-09-19 16:16:47,729 - database_manager - INFO - 成功更新vendor: fe8fcd49-ea3d-43a8-b8fb-3b3429986603
2025-09-19 16:16:47,729 - __main__ - INFO - 成功更新vendor: fe8fcd49-ea3d-43a8-b8fb-3b3429986603
2025-09-19 16:16:47,730 - database_manager - INFO - 成功删除vendor联系方式: fe8fcd49-ea3d-43a8-b8fb-3b3429986603
2025-09-19 16:16:47,731 - __main__ - INFO - 成功处理supplier: 100087
2025-09-19 16:16:47,731 - __main__ - INFO - 进度: 89/215
2025-09-19 16:16:47,731 - __main__ - INFO - 处理supplier: 100088
2025-09-19 16:16:47,732 - __main__ - WARNING - 未找到对应的vendor，跳过: 100088
2025-09-19 16:16:47,732 - __main__ - INFO - 进度: 90/215
2025-09-19 16:16:47,732 - __main__ - INFO - 处理supplier: 100089
2025-09-19 16:16:47,732 - __main__ - INFO - 找到对应vendor: 71451235-4c7d-4001-a0d2-d1d03244cb4d
2025-09-19 16:16:47,733 - database_manager - INFO - 成功更新vendor: 71451235-4c7d-4001-a0d2-d1d03244cb4d
2025-09-19 16:16:47,733 - __main__ - INFO - 成功更新vendor: 71451235-4c7d-4001-a0d2-d1d03244cb4d
2025-09-19 16:16:47,734 - database_manager - INFO - 成功删除vendor联系方式: 71451235-4c7d-4001-a0d2-d1d03244cb4d
2025-09-19 16:16:47,736 - database_manager - ERROR - 插入地址失败: (psycopg2.errors.NotNullViolation) null value in column "purpose" of relation "address" violates not-null constraint
DETAIL:  Failing row contains (f4a3c99f-28f7-4909-8e68-43110d82b3b6, VENDOR, 71451235-4c7d-4001-a0d2-d1d03244cb4d, 2252 Avenida Empresa 
Suite 100, Rancho Santa Margarita, CA, 92688, USA, null, null, null, 2025-09-19 16:16:47.736178, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO address (
                id, entity_type, entity_id, street_address, city, state, 
                postal_code, country, directions, purpose, additional_lines,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(street_address)s, %(city)s, %(state)s,
                %(postal_code)s, %(country)s, %(directions)s, %(purpose)s, %(additional_lines)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'f4a3c99f-28f7-4909-8e68-43110d82b3b6', 'entity_type': 'VENDOR', 'entity_id': UUID('71451235-4c7d-4001-a0d2-d1d03244cb4d'), 'street_address': '2252 Avenida Empresa \nSuite 100', 'city': 'Rancho Santa Margarita', 'state': 'CA', 'postal_code': '92688', 'country': 'USA', 'directions': None, 'purpose': None, 'additional_lines': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 736178), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,737 - __main__ - ERROR - 插入地址失败: 71451235-4c7d-4001-a0d2-d1d03244cb4d
2025-09-19 16:16:47,739 - __main__ - INFO - 成功处理supplier: 100089
2025-09-19 16:16:47,739 - __main__ - INFO - 进度: 91/215
2025-09-19 16:16:47,739 - __main__ - INFO - 处理supplier: 100090
2025-09-19 16:16:47,739 - __main__ - WARNING - 未找到对应的vendor，跳过: 100090
2025-09-19 16:16:47,739 - __main__ - INFO - 进度: 92/215
2025-09-19 16:16:47,739 - __main__ - INFO - 处理supplier: 100091
2025-09-19 16:16:47,740 - __main__ - INFO - 找到对应vendor: d0f1b85a-a9b8-4257-8a2d-980b0038994e
2025-09-19 16:16:47,741 - database_manager - INFO - 成功更新vendor: d0f1b85a-a9b8-4257-8a2d-980b0038994e
2025-09-19 16:16:47,741 - __main__ - INFO - 成功更新vendor: d0f1b85a-a9b8-4257-8a2d-980b0038994e
2025-09-19 16:16:47,742 - database_manager - INFO - 成功删除vendor联系方式: d0f1b85a-a9b8-4257-8a2d-980b0038994e
2025-09-19 16:16:47,748 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (701e17ab-ce4d-4bff-bf91-b275a79cc55f, VENDOR, d0f1b85a-a9b8-4257-8a2d-980b0038994e, null, ************, null, 2025-09-19 16:16:47.747858, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '701e17ab-ce4d-4bff-bf91-b275a79cc55f', 'entity_type': 'VENDOR', 'entity_id': UUID('d0f1b85a-a9b8-4257-8a2d-980b0038994e'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 747858), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,748 - __main__ - ERROR - 插入电话失败: d0f1b85a-a9b8-4257-8a2d-980b0038994e
2025-09-19 16:16:47,749 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (f494c5d3-fddd-46b3-9d51-4a9a09b22cc4, VENDOR, d0f1b85a-a9b8-4257-8a2d-980b0038994e, null, ************, null, 2025-09-19 16:16:47.748827, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'f494c5d3-fddd-46b3-9d51-4a9a09b22cc4', 'entity_type': 'VENDOR', 'entity_id': UUID('d0f1b85a-a9b8-4257-8a2d-980b0038994e'), 'phone_type': None, 'phone_number': '************', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 748827), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,749 - __main__ - ERROR - 插入电话失败: d0f1b85a-a9b8-4257-8a2d-980b0038994e
2025-09-19 16:16:47,750 - database_manager - ERROR - 插入网址失败: (psycopg2.errors.NotNullViolation) null value in column "web_address_type" of relation "web_address" violates not-null constraint
DETAIL:  Failing row contains (e07e9ae8-2d7e-4747-bf70-5d4a7ff09e69, VENDOR, d0f1b85a-a9b8-4257-8a2d-980b0038994e, null, https://aacashandcarry.com/, 2025-09-19 16:16:47.749706, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO web_address (
                id, entity_type, entity_id, web_address_type, web_address,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(web_address_type)s, %(web_address)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'e07e9ae8-2d7e-4747-bf70-5d4a7ff09e69', 'entity_type': 'VENDOR', 'entity_id': UUID('d0f1b85a-a9b8-4257-8a2d-980b0038994e'), 'web_address_type': None, 'web_address': 'https://aacashandcarry.com/', 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 749706), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,750 - __main__ - ERROR - 插入网址失败: d0f1b85a-a9b8-4257-8a2d-980b0038994e
2025-09-19 16:16:47,750 - __main__ - INFO - 成功处理supplier: 100091
2025-09-19 16:16:47,750 - __main__ - INFO - 进度: 93/215
2025-09-19 16:16:47,750 - __main__ - INFO - 处理supplier: 100092
2025-09-19 16:16:47,751 - __main__ - INFO - 找到对应vendor: be0a63c3-03dc-4f3c-8ab7-8b20ae040d5b
2025-09-19 16:16:47,751 - database_manager - INFO - 成功更新vendor: be0a63c3-03dc-4f3c-8ab7-8b20ae040d5b
2025-09-19 16:16:47,752 - __main__ - INFO - 成功更新vendor: be0a63c3-03dc-4f3c-8ab7-8b20ae040d5b
2025-09-19 16:16:47,753 - database_manager - INFO - 成功删除vendor联系方式: be0a63c3-03dc-4f3c-8ab7-8b20ae040d5b
2025-09-19 16:16:47,754 - __main__ - INFO - 成功处理supplier: 100092
2025-09-19 16:16:47,754 - __main__ - INFO - 进度: 94/215
2025-09-19 16:16:47,754 - __main__ - INFO - 处理supplier: 100093
2025-09-19 16:16:47,754 - __main__ - INFO - 找到对应vendor: 187d6ae5-9b38-4d82-9bd0-c3d3c828ba56
2025-09-19 16:16:47,755 - database_manager - INFO - 成功更新vendor: 187d6ae5-9b38-4d82-9bd0-c3d3c828ba56
2025-09-19 16:16:47,755 - __main__ - INFO - 成功更新vendor: 187d6ae5-9b38-4d82-9bd0-c3d3c828ba56
2025-09-19 16:16:47,757 - database_manager - INFO - 成功删除vendor联系方式: 187d6ae5-9b38-4d82-9bd0-c3d3c828ba56
2025-09-19 16:16:47,757 - __main__ - INFO - 成功处理supplier: 100093
2025-09-19 16:16:47,757 - __main__ - INFO - 进度: 95/215
2025-09-19 16:16:47,757 - __main__ - INFO - 处理supplier: 100094
2025-09-19 16:16:47,758 - __main__ - INFO - 找到对应vendor: 567f5386-8c1a-48af-95b9-fd89956ffe82
2025-09-19 16:16:47,759 - database_manager - INFO - 成功更新vendor: 567f5386-8c1a-48af-95b9-fd89956ffe82
2025-09-19 16:16:47,759 - __main__ - INFO - 成功更新vendor: 567f5386-8c1a-48af-95b9-fd89956ffe82
2025-09-19 16:16:47,760 - database_manager - INFO - 成功删除vendor联系方式: 567f5386-8c1a-48af-95b9-fd89956ffe82
2025-09-19 16:16:47,761 - __main__ - INFO - 成功处理supplier: 100094
2025-09-19 16:16:47,761 - __main__ - INFO - 进度: 96/215
2025-09-19 16:16:47,761 - __main__ - INFO - 处理supplier: 100095
2025-09-19 16:16:47,762 - __main__ - INFO - 找到对应vendor: 54e5e1c3-029e-4b4d-af01-57acee317a60
2025-09-19 16:16:47,763 - database_manager - INFO - 成功更新vendor: 54e5e1c3-029e-4b4d-af01-57acee317a60
2025-09-19 16:16:47,763 - __main__ - INFO - 成功更新vendor: 54e5e1c3-029e-4b4d-af01-57acee317a60
2025-09-19 16:16:47,764 - database_manager - INFO - 成功删除vendor联系方式: 54e5e1c3-029e-4b4d-af01-57acee317a60
2025-09-19 16:16:47,766 - __main__ - INFO - 成功处理supplier: 100095
2025-09-19 16:16:47,766 - __main__ - INFO - 进度: 97/215
2025-09-19 16:16:47,766 - __main__ - INFO - 处理supplier: 100096
2025-09-19 16:16:47,767 - __main__ - WARNING - 未找到对应的vendor，跳过: 100096
2025-09-19 16:16:47,767 - __main__ - INFO - 进度: 98/215
2025-09-19 16:16:47,767 - __main__ - INFO - 处理supplier: 100097
2025-09-19 16:16:47,768 - __main__ - INFO - 找到对应vendor: 83c78006-3fd0-4e72-8793-d9a506b6e5f6
2025-09-19 16:16:47,768 - database_manager - INFO - 成功更新vendor: 83c78006-3fd0-4e72-8793-d9a506b6e5f6
2025-09-19 16:16:47,769 - __main__ - INFO - 成功更新vendor: 83c78006-3fd0-4e72-8793-d9a506b6e5f6
2025-09-19 16:16:47,770 - database_manager - INFO - 成功删除vendor联系方式: 83c78006-3fd0-4e72-8793-d9a506b6e5f6
2025-09-19 16:16:47,771 - __main__ - INFO - 成功处理supplier: 100097
2025-09-19 16:16:47,771 - __main__ - INFO - 进度: 99/215
2025-09-19 16:16:47,771 - __main__ - INFO - 处理supplier: 100098
2025-09-19 16:16:47,772 - __main__ - INFO - 找到对应vendor: d6a7d20a-3104-445f-88ad-cdfc1553ddc8
2025-09-19 16:16:47,773 - database_manager - INFO - 成功更新vendor: d6a7d20a-3104-445f-88ad-cdfc1553ddc8
2025-09-19 16:16:47,773 - __main__ - INFO - 成功更新vendor: d6a7d20a-3104-445f-88ad-cdfc1553ddc8
2025-09-19 16:16:47,774 - database_manager - INFO - 成功删除vendor联系方式: d6a7d20a-3104-445f-88ad-cdfc1553ddc8
2025-09-19 16:16:47,775 - __main__ - INFO - 成功处理supplier: 100098
2025-09-19 16:16:47,775 - __main__ - INFO - 进度: 100/215
2025-09-19 16:16:47,775 - __main__ - INFO - 处理supplier: 100099
2025-09-19 16:16:47,776 - __main__ - WARNING - 未找到对应的vendor，跳过: 100099
2025-09-19 16:16:47,776 - __main__ - INFO - 进度: 101/215
2025-09-19 16:16:47,776 - __main__ - INFO - 处理supplier: 100100
2025-09-19 16:16:47,777 - __main__ - INFO - 找到对应vendor: 09028e57-e742-499b-b959-bcfcaa0198bb
2025-09-19 16:16:47,778 - database_manager - INFO - 成功更新vendor: 09028e57-e742-499b-b959-bcfcaa0198bb
2025-09-19 16:16:47,778 - __main__ - INFO - 成功更新vendor: 09028e57-e742-499b-b959-bcfcaa0198bb
2025-09-19 16:16:47,779 - database_manager - INFO - 成功删除vendor联系方式: 09028e57-e742-499b-b959-bcfcaa0198bb
2025-09-19 16:16:47,781 - database_manager - ERROR - 插入地址失败: (psycopg2.errors.NotNullViolation) null value in column "purpose" of relation "address" violates not-null constraint
DETAIL:  Failing row contains (22a7ed41-4556-4a60-a333-15d21a34def9, VENDOR, 09028e57-e742-499b-b959-bcfcaa0198bb, 12500 Slauson Ave, Bldg C2, Santa Fe Springs, CA, 90670, null, null, null, null, 2025-09-19 16:16:47.780629, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO address (
                id, entity_type, entity_id, street_address, city, state, 
                postal_code, country, directions, purpose, additional_lines,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(street_address)s, %(city)s, %(state)s,
                %(postal_code)s, %(country)s, %(directions)s, %(purpose)s, %(additional_lines)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '22a7ed41-4556-4a60-a333-15d21a34def9', 'entity_type': 'VENDOR', 'entity_id': UUID('09028e57-e742-499b-b959-bcfcaa0198bb'), 'street_address': '12500 Slauson Ave, Bldg C2', 'city': 'Santa Fe Springs', 'state': 'CA', 'postal_code': '90670', 'country': None, 'directions': None, 'purpose': None, 'additional_lines': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 780629), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,781 - __main__ - ERROR - 插入地址失败: 09028e57-e742-499b-b959-bcfcaa0198bb
2025-09-19 16:16:47,782 - __main__ - INFO - 成功处理supplier: 100100
2025-09-19 16:16:47,783 - __main__ - INFO - 进度: 102/215
2025-09-19 16:16:47,783 - __main__ - INFO - 处理supplier: 100101
2025-09-19 16:16:47,783 - __main__ - INFO - 找到对应vendor: e8e274f6-3a38-4de1-ab2c-c703cc91c630
2025-09-19 16:16:47,784 - database_manager - INFO - 成功更新vendor: e8e274f6-3a38-4de1-ab2c-c703cc91c630
2025-09-19 16:16:47,784 - __main__ - INFO - 成功更新vendor: e8e274f6-3a38-4de1-ab2c-c703cc91c630
2025-09-19 16:16:47,785 - database_manager - INFO - 成功删除vendor联系方式: e8e274f6-3a38-4de1-ab2c-c703cc91c630
2025-09-19 16:16:47,786 - __main__ - INFO - 成功处理supplier: 100101
2025-09-19 16:16:47,786 - __main__ - INFO - 进度: 103/215
2025-09-19 16:16:47,786 - __main__ - INFO - 处理supplier: 100102
2025-09-19 16:16:47,787 - __main__ - WARNING - 未找到对应的vendor，跳过: 100102
2025-09-19 16:16:47,787 - __main__ - INFO - 进度: 104/215
2025-09-19 16:16:47,787 - __main__ - INFO - 处理supplier: 100103
2025-09-19 16:16:47,787 - __main__ - WARNING - 未找到对应的vendor，跳过: 100103
2025-09-19 16:16:47,787 - __main__ - INFO - 进度: 105/215
2025-09-19 16:16:47,787 - __main__ - INFO - 处理supplier: 100104
2025-09-19 16:16:47,788 - __main__ - WARNING - 未找到对应的vendor，跳过: 100104
2025-09-19 16:16:47,788 - __main__ - INFO - 进度: 106/215
2025-09-19 16:16:47,788 - __main__ - INFO - 处理supplier: 100105
2025-09-19 16:16:47,789 - __main__ - INFO - 找到对应vendor: a9b1a81f-25e4-4695-adbc-18cb98d7722c
2025-09-19 16:16:47,789 - database_manager - INFO - 成功更新vendor: a9b1a81f-25e4-4695-adbc-18cb98d7722c
2025-09-19 16:16:47,790 - __main__ - INFO - 成功更新vendor: a9b1a81f-25e4-4695-adbc-18cb98d7722c
2025-09-19 16:16:47,791 - database_manager - INFO - 成功删除vendor联系方式: a9b1a81f-25e4-4695-adbc-18cb98d7722c
2025-09-19 16:16:47,799 - database_manager - ERROR - 插入网址失败: (psycopg2.errors.NotNullViolation) null value in column "web_address_type" of relation "web_address" violates not-null constraint
DETAIL:  Failing row contains (bac3bd72-1ea7-4c9e-b27e-0d76f4e01166, VENDOR, a9b1a81f-25e4-4695-adbc-18cb98d7722c, null, www.summithillsales.com, 2025-09-19 16:16:47.79847, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO web_address (
                id, entity_type, entity_id, web_address_type, web_address,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(web_address_type)s, %(web_address)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'bac3bd72-1ea7-4c9e-b27e-0d76f4e01166', 'entity_type': 'VENDOR', 'entity_id': UUID('a9b1a81f-25e4-4695-adbc-18cb98d7722c'), 'web_address_type': None, 'web_address': 'www.summithillsales.com', 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 798470), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,799 - __main__ - ERROR - 插入网址失败: a9b1a81f-25e4-4695-adbc-18cb98d7722c
2025-09-19 16:16:47,799 - __main__ - INFO - 成功处理supplier: 100105
2025-09-19 16:16:47,799 - __main__ - INFO - 进度: 107/215
2025-09-19 16:16:47,799 - __main__ - INFO - 处理supplier: 100106
2025-09-19 16:16:47,800 - __main__ - INFO - 找到对应vendor: fcee8cc0-4924-40ca-87ba-acb09127e13e
2025-09-19 16:16:47,800 - database_manager - INFO - 成功更新vendor: fcee8cc0-4924-40ca-87ba-acb09127e13e
2025-09-19 16:16:47,800 - __main__ - INFO - 成功更新vendor: fcee8cc0-4924-40ca-87ba-acb09127e13e
2025-09-19 16:16:47,802 - database_manager - INFO - 成功删除vendor联系方式: fcee8cc0-4924-40ca-87ba-acb09127e13e
2025-09-19 16:16:47,804 - database_manager - ERROR - 插入地址失败: (psycopg2.errors.NotNullViolation) null value in column "purpose" of relation "address" violates not-null constraint
DETAIL:  Failing row contains (38e96142-4c06-426b-98b8-0a80d4e0147c, VENDOR, fcee8cc0-4924-40ca-87ba-acb09127e13e, 1100 E 8TH Street, Los Angeles, CA, 90021, null, null, null, null, 2025-09-19 16:16:47.803566, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO address (
                id, entity_type, entity_id, street_address, city, state, 
                postal_code, country, directions, purpose, additional_lines,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(street_address)s, %(city)s, %(state)s,
                %(postal_code)s, %(country)s, %(directions)s, %(purpose)s, %(additional_lines)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '38e96142-4c06-426b-98b8-0a80d4e0147c', 'entity_type': 'VENDOR', 'entity_id': UUID('fcee8cc0-4924-40ca-87ba-acb09127e13e'), 'street_address': '1100 E 8TH Street', 'city': 'Los Angeles', 'state': 'CA', 'postal_code': '90021', 'country': None, 'directions': None, 'purpose': None, 'additional_lines': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 803566), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,804 - __main__ - ERROR - 插入地址失败: fcee8cc0-4924-40ca-87ba-acb09127e13e
2025-09-19 16:16:47,806 - __main__ - INFO - 成功处理supplier: 100106
2025-09-19 16:16:47,806 - __main__ - INFO - 进度: 108/215
2025-09-19 16:16:47,806 - __main__ - INFO - 处理supplier: 100107
2025-09-19 16:16:47,807 - __main__ - INFO - 找到对应vendor: 00b6e480-09ba-4088-a509-1250388ce746
2025-09-19 16:16:47,807 - database_manager - INFO - 成功更新vendor: 00b6e480-09ba-4088-a509-1250388ce746
2025-09-19 16:16:47,807 - __main__ - INFO - 成功更新vendor: 00b6e480-09ba-4088-a509-1250388ce746
2025-09-19 16:16:47,808 - database_manager - INFO - 成功删除vendor联系方式: 00b6e480-09ba-4088-a509-1250388ce746
2025-09-19 16:16:47,810 - database_manager - ERROR - 插入地址失败: (psycopg2.errors.NotNullViolation) null value in column "purpose" of relation "address" violates not-null constraint
DETAIL:  Failing row contains (72da8a21-a779-4d9a-b3a1-9e118a05a4ee, VENDOR, 00b6e480-09ba-4088-a509-1250388ce746, 19801 Nordhoff Pl #107, Chatsworth, CA, 91311, USA, null, null, null, 2025-09-19 16:16:47.809646, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO address (
                id, entity_type, entity_id, street_address, city, state, 
                postal_code, country, directions, purpose, additional_lines,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(street_address)s, %(city)s, %(state)s,
                %(postal_code)s, %(country)s, %(directions)s, %(purpose)s, %(additional_lines)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '72da8a21-a779-4d9a-b3a1-9e118a05a4ee', 'entity_type': 'VENDOR', 'entity_id': UUID('00b6e480-09ba-4088-a509-1250388ce746'), 'street_address': '19801 Nordhoff Pl #107', 'city': 'Chatsworth', 'state': 'CA', 'postal_code': '91311', 'country': 'USA', 'directions': None, 'purpose': None, 'additional_lines': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 809646), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,810 - __main__ - ERROR - 插入地址失败: 00b6e480-09ba-4088-a509-1250388ce746
2025-09-19 16:16:47,813 - database_manager - ERROR - 插入网址失败: (psycopg2.errors.NotNullViolation) null value in column "web_address_type" of relation "web_address" violates not-null constraint
DETAIL:  Failing row contains (56f0e509-922c-4ade-8c37-c6cb40856494, VENDOR, 00b6e480-09ba-4088-a509-1250388ce746, null, emobii.com, 2025-09-19 16:16:47.812434, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO web_address (
                id, entity_type, entity_id, web_address_type, web_address,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(web_address_type)s, %(web_address)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '56f0e509-922c-4ade-8c37-c6cb40856494', 'entity_type': 'VENDOR', 'entity_id': UUID('00b6e480-09ba-4088-a509-1250388ce746'), 'web_address_type': None, 'web_address': 'emobii.com', 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 812434), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,813 - __main__ - ERROR - 插入网址失败: 00b6e480-09ba-4088-a509-1250388ce746
2025-09-19 16:16:47,813 - __main__ - INFO - 成功处理supplier: 100107
2025-09-19 16:16:47,813 - __main__ - INFO - 进度: 109/215
2025-09-19 16:16:47,813 - __main__ - INFO - 处理supplier: 100108
2025-09-19 16:16:47,813 - __main__ - INFO - 找到对应vendor: b54d12ad-7820-4492-ae05-9d1f2df4e92d
2025-09-19 16:16:47,814 - database_manager - INFO - 成功更新vendor: b54d12ad-7820-4492-ae05-9d1f2df4e92d
2025-09-19 16:16:47,814 - __main__ - INFO - 成功更新vendor: b54d12ad-7820-4492-ae05-9d1f2df4e92d
2025-09-19 16:16:47,815 - database_manager - INFO - 成功删除vendor联系方式: b54d12ad-7820-4492-ae05-9d1f2df4e92d
2025-09-19 16:16:47,816 - __main__ - INFO - 成功处理supplier: 100108
2025-09-19 16:16:47,816 - __main__ - INFO - 进度: 110/215
2025-09-19 16:16:47,816 - __main__ - INFO - 处理supplier: 100109
2025-09-19 16:16:47,816 - __main__ - INFO - 找到对应vendor: e4dc22fc-26ec-41b5-83f5-b1c010827edb
2025-09-19 16:16:47,817 - database_manager - INFO - 成功更新vendor: e4dc22fc-26ec-41b5-83f5-b1c010827edb
2025-09-19 16:16:47,817 - __main__ - INFO - 成功更新vendor: e4dc22fc-26ec-41b5-83f5-b1c010827edb
2025-09-19 16:16:47,818 - database_manager - INFO - 成功删除vendor联系方式: e4dc22fc-26ec-41b5-83f5-b1c010827edb
2025-09-19 16:16:47,822 - database_manager - ERROR - 插入网址失败: (psycopg2.errors.NotNullViolation) null value in column "web_address_type" of relation "web_address" violates not-null constraint
DETAIL:  Failing row contains (c99893f3-b348-499b-9c37-f1bea6302130, VENDOR, e4dc22fc-26ec-41b5-83f5-b1c010827edb, null, www.twang.com, 2025-09-19 16:16:47.822338, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO web_address (
                id, entity_type, entity_id, web_address_type, web_address,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(web_address_type)s, %(web_address)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'c99893f3-b348-499b-9c37-f1bea6302130', 'entity_type': 'VENDOR', 'entity_id': UUID('e4dc22fc-26ec-41b5-83f5-b1c010827edb'), 'web_address_type': None, 'web_address': 'www.twang.com', 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 822338), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,823 - __main__ - ERROR - 插入网址失败: e4dc22fc-26ec-41b5-83f5-b1c010827edb
2025-09-19 16:16:47,823 - __main__ - INFO - 成功处理supplier: 100109
2025-09-19 16:16:47,823 - __main__ - INFO - 进度: 111/215
2025-09-19 16:16:47,823 - __main__ - INFO - 处理supplier: 100110
2025-09-19 16:16:47,823 - __main__ - INFO - 找到对应vendor: 0f396647-1cc4-4a99-892b-b42ee1d82423
2025-09-19 16:16:47,824 - database_manager - INFO - 成功更新vendor: 0f396647-1cc4-4a99-892b-b42ee1d82423
2025-09-19 16:16:47,824 - __main__ - INFO - 成功更新vendor: 0f396647-1cc4-4a99-892b-b42ee1d82423
2025-09-19 16:16:47,825 - database_manager - INFO - 成功删除vendor联系方式: 0f396647-1cc4-4a99-892b-b42ee1d82423
2025-09-19 16:16:47,825 - __main__ - INFO - 成功处理supplier: 100110
2025-09-19 16:16:47,825 - __main__ - INFO - 进度: 112/215
2025-09-19 16:16:47,825 - __main__ - INFO - 处理supplier: 100111
2025-09-19 16:16:47,826 - __main__ - INFO - 找到对应vendor: 84d85c3e-3994-447c-8407-1f6444a73229
2025-09-19 16:16:47,826 - database_manager - INFO - 成功更新vendor: 84d85c3e-3994-447c-8407-1f6444a73229
2025-09-19 16:16:47,827 - __main__ - INFO - 成功更新vendor: 84d85c3e-3994-447c-8407-1f6444a73229
2025-09-19 16:16:47,828 - database_manager - INFO - 成功删除vendor联系方式: 84d85c3e-3994-447c-8407-1f6444a73229
2025-09-19 16:16:47,831 - database_manager - ERROR - 插入网址失败: (psycopg2.errors.NotNullViolation) null value in column "web_address_type" of relation "web_address" violates not-null constraint
DETAIL:  Failing row contains (40903c9c-6406-4e54-84fd-e6f2d70d2bf0, VENDOR, 84d85c3e-3994-447c-8407-1f6444a73229, null, www.7starsavings.com, 2025-09-19 16:16:47.831021, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO web_address (
                id, entity_type, entity_id, web_address_type, web_address,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(web_address_type)s, %(web_address)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '40903c9c-6406-4e54-84fd-e6f2d70d2bf0', 'entity_type': 'VENDOR', 'entity_id': UUID('84d85c3e-3994-447c-8407-1f6444a73229'), 'web_address_type': None, 'web_address': 'www.7starsavings.com', 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 831021), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,831 - __main__ - ERROR - 插入网址失败: 84d85c3e-3994-447c-8407-1f6444a73229
2025-09-19 16:16:47,831 - __main__ - INFO - 成功处理supplier: 100111
2025-09-19 16:16:47,831 - __main__ - INFO - 进度: 113/215
2025-09-19 16:16:47,831 - __main__ - INFO - 处理supplier: 100112
2025-09-19 16:16:47,832 - __main__ - INFO - 找到对应vendor: 5692de00-b42a-4e3e-a4d4-7a3c9cf8e5fa
2025-09-19 16:16:47,832 - database_manager - INFO - 成功更新vendor: 5692de00-b42a-4e3e-a4d4-7a3c9cf8e5fa
2025-09-19 16:16:47,832 - __main__ - INFO - 成功更新vendor: 5692de00-b42a-4e3e-a4d4-7a3c9cf8e5fa
2025-09-19 16:16:47,833 - database_manager - INFO - 成功删除vendor联系方式: 5692de00-b42a-4e3e-a4d4-7a3c9cf8e5fa
2025-09-19 16:16:47,836 - __main__ - INFO - 成功处理supplier: 100112
2025-09-19 16:16:47,836 - __main__ - INFO - 进度: 114/215
2025-09-19 16:16:47,836 - __main__ - INFO - 处理supplier: 100113
2025-09-19 16:16:47,836 - __main__ - WARNING - 未找到对应的vendor，跳过: 100113
2025-09-19 16:16:47,836 - __main__ - INFO - 进度: 115/215
2025-09-19 16:16:47,836 - __main__ - INFO - 处理supplier: 100114
2025-09-19 16:16:47,837 - __main__ - INFO - 找到对应vendor: 413d2b3c-009a-4c03-b760-dee054417ea2
2025-09-19 16:16:47,837 - database_manager - INFO - 成功更新vendor: 413d2b3c-009a-4c03-b760-dee054417ea2
2025-09-19 16:16:47,838 - __main__ - INFO - 成功更新vendor: 413d2b3c-009a-4c03-b760-dee054417ea2
2025-09-19 16:16:47,839 - database_manager - INFO - 成功删除vendor联系方式: 413d2b3c-009a-4c03-b760-dee054417ea2
2025-09-19 16:16:47,840 - database_manager - ERROR - 插入地址失败: (psycopg2.errors.NotNullViolation) null value in column "purpose" of relation "address" violates not-null constraint
DETAIL:  Failing row contains (0853dcd2-8ada-464a-92b7-91644e04675c, VENDOR, 413d2b3c-009a-4c03-b760-dee054417ea2, 1270 Avenue of the Americas, 7th Floor, New York, NY, 10020, null, null, null, null, 2025-09-19 16:16:47.839678, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO address (
                id, entity_type, entity_id, street_address, city, state, 
                postal_code, country, directions, purpose, additional_lines,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(street_address)s, %(city)s, %(state)s,
                %(postal_code)s, %(country)s, %(directions)s, %(purpose)s, %(additional_lines)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '0853dcd2-8ada-464a-92b7-91644e04675c', 'entity_type': 'VENDOR', 'entity_id': UUID('413d2b3c-009a-4c03-b760-dee054417ea2'), 'street_address': '1270 Avenue of the Americas, 7th Floor', 'city': 'New York', 'state': 'NY', 'postal_code': '10020', 'country': None, 'directions': None, 'purpose': None, 'additional_lines': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 839678), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,840 - __main__ - ERROR - 插入地址失败: 413d2b3c-009a-4c03-b760-dee054417ea2
2025-09-19 16:16:47,842 - __main__ - INFO - 成功处理supplier: 100114
2025-09-19 16:16:47,842 - __main__ - INFO - 进度: 116/215
2025-09-19 16:16:47,842 - __main__ - INFO - 处理supplier: 100115
2025-09-19 16:16:47,843 - __main__ - INFO - 找到对应vendor: 4aab9977-a25b-4b7b-b410-dd218e6ab292
2025-09-19 16:16:47,843 - database_manager - INFO - 成功更新vendor: 4aab9977-a25b-4b7b-b410-dd218e6ab292
2025-09-19 16:16:47,843 - __main__ - INFO - 成功更新vendor: 4aab9977-a25b-4b7b-b410-dd218e6ab292
2025-09-19 16:16:47,844 - database_manager - INFO - 成功删除vendor联系方式: 4aab9977-a25b-4b7b-b410-dd218e6ab292
2025-09-19 16:16:47,845 - __main__ - INFO - 成功处理supplier: 100115
2025-09-19 16:16:47,845 - __main__ - INFO - 进度: 117/215
2025-09-19 16:16:47,845 - __main__ - INFO - 处理supplier: 100116
2025-09-19 16:16:47,845 - __main__ - WARNING - 未找到对应的vendor，跳过: 100116
2025-09-19 16:16:47,845 - __main__ - INFO - 进度: 118/215
2025-09-19 16:16:47,846 - __main__ - INFO - 处理supplier: 100117
2025-09-19 16:16:47,846 - __main__ - WARNING - 未找到对应的vendor，跳过: 100117
2025-09-19 16:16:47,846 - __main__ - INFO - 进度: 119/215
2025-09-19 16:16:47,846 - __main__ - INFO - 处理supplier: 100118
2025-09-19 16:16:47,847 - __main__ - INFO - 找到对应vendor: 6ad7e357-7cd9-4d30-a6bd-063d4006c70f
2025-09-19 16:16:47,847 - database_manager - INFO - 成功删除vendor联系方式: 6ad7e357-7cd9-4d30-a6bd-063d4006c70f
2025-09-19 16:16:47,848 - __main__ - INFO - 成功处理supplier: 100118
2025-09-19 16:16:47,848 - __main__ - INFO - 进度: 120/215
2025-09-19 16:16:47,848 - __main__ - INFO - 处理supplier: 100119
2025-09-19 16:16:47,849 - __main__ - INFO - 找到对应vendor: 267b9430-07c0-40ae-9d43-90a2535fb6cb
2025-09-19 16:16:47,849 - database_manager - INFO - 成功更新vendor: 267b9430-07c0-40ae-9d43-90a2535fb6cb
2025-09-19 16:16:47,849 - __main__ - INFO - 成功更新vendor: 267b9430-07c0-40ae-9d43-90a2535fb6cb
2025-09-19 16:16:47,850 - database_manager - INFO - 成功删除vendor联系方式: 267b9430-07c0-40ae-9d43-90a2535fb6cb
2025-09-19 16:16:47,851 - __main__ - INFO - 成功处理supplier: 100119
2025-09-19 16:16:47,851 - __main__ - INFO - 进度: 121/215
2025-09-19 16:16:47,851 - __main__ - INFO - 处理supplier: 100120
2025-09-19 16:16:47,852 - __main__ - INFO - 找到对应vendor: 39d71d29-d064-4466-89e4-e2fd70627970
2025-09-19 16:16:47,852 - database_manager - INFO - 成功更新vendor: 39d71d29-d064-4466-89e4-e2fd70627970
2025-09-19 16:16:47,853 - __main__ - INFO - 成功更新vendor: 39d71d29-d064-4466-89e4-e2fd70627970
2025-09-19 16:16:47,854 - database_manager - INFO - 成功删除vendor联系方式: 39d71d29-d064-4466-89e4-e2fd70627970
2025-09-19 16:16:47,855 - database_manager - ERROR - 插入地址失败: (psycopg2.errors.NotNullViolation) null value in column "purpose" of relation "address" violates not-null constraint
DETAIL:  Failing row contains (f0b3bf46-bbea-4309-99a4-86f4e753a0a2, VENDOR, 39d71d29-d064-4466-89e4-e2fd70627970, 8831 Shirley Ave, Northridge, CA, 91324, USA, null, null, null, 2025-09-19 16:16:47.854708, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO address (
                id, entity_type, entity_id, street_address, city, state, 
                postal_code, country, directions, purpose, additional_lines,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(street_address)s, %(city)s, %(state)s,
                %(postal_code)s, %(country)s, %(directions)s, %(purpose)s, %(additional_lines)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'f0b3bf46-bbea-4309-99a4-86f4e753a0a2', 'entity_type': 'VENDOR', 'entity_id': UUID('39d71d29-d064-4466-89e4-e2fd70627970'), 'street_address': '8831 Shirley Ave', 'city': 'Northridge', 'state': 'CA', 'postal_code': '91324', 'country': 'USA', 'directions': None, 'purpose': None, 'additional_lines': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 854708), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,855 - __main__ - ERROR - 插入地址失败: 39d71d29-d064-4466-89e4-e2fd70627970
2025-09-19 16:16:47,857 - __main__ - INFO - 成功处理supplier: 100120
2025-09-19 16:16:47,857 - __main__ - INFO - 进度: 122/215
2025-09-19 16:16:47,857 - __main__ - INFO - 处理supplier: 100121
2025-09-19 16:16:47,857 - __main__ - WARNING - 未找到对应的vendor，跳过: 100121
2025-09-19 16:16:47,857 - __main__ - INFO - 进度: 123/215
2025-09-19 16:16:47,857 - __main__ - INFO - 处理supplier: 100122
2025-09-19 16:16:47,858 - __main__ - WARNING - 未找到对应的vendor，跳过: 100122
2025-09-19 16:16:47,858 - __main__ - INFO - 进度: 124/215
2025-09-19 16:16:47,858 - __main__ - INFO - 处理supplier: 100123
2025-09-19 16:16:47,858 - __main__ - WARNING - 未找到对应的vendor，跳过: 100123
2025-09-19 16:16:47,858 - __main__ - INFO - 进度: 125/215
2025-09-19 16:16:47,858 - __main__ - INFO - 处理supplier: 100124
2025-09-19 16:16:47,859 - __main__ - WARNING - 未找到对应的vendor，跳过: 100124
2025-09-19 16:16:47,859 - __main__ - INFO - 进度: 126/215
2025-09-19 16:16:47,859 - __main__ - INFO - 处理supplier: 100125
2025-09-19 16:16:47,859 - __main__ - WARNING - 未找到对应的vendor，跳过: 100125
2025-09-19 16:16:47,859 - __main__ - INFO - 进度: 127/215
2025-09-19 16:16:47,859 - __main__ - INFO - 处理supplier: 100126
2025-09-19 16:16:47,860 - __main__ - WARNING - 未找到对应的vendor，跳过: 100126
2025-09-19 16:16:47,860 - __main__ - INFO - 进度: 128/215
2025-09-19 16:16:47,860 - __main__ - INFO - 处理supplier: 100127
2025-09-19 16:16:47,861 - __main__ - WARNING - 未找到对应的vendor，跳过: 100127
2025-09-19 16:16:47,861 - __main__ - INFO - 进度: 129/215
2025-09-19 16:16:47,861 - __main__ - INFO - 处理supplier: 100128
2025-09-19 16:16:47,861 - __main__ - WARNING - 未找到对应的vendor，跳过: 100128
2025-09-19 16:16:47,861 - __main__ - INFO - 进度: 130/215
2025-09-19 16:16:47,861 - __main__ - INFO - 处理supplier: 100129
2025-09-19 16:16:47,862 - __main__ - WARNING - 未找到对应的vendor，跳过: 100129
2025-09-19 16:16:47,862 - __main__ - INFO - 进度: 131/215
2025-09-19 16:16:47,862 - __main__ - INFO - 处理supplier: 100130
2025-09-19 16:16:47,862 - __main__ - WARNING - 未找到对应的vendor，跳过: 100130
2025-09-19 16:16:47,862 - __main__ - INFO - 进度: 132/215
2025-09-19 16:16:47,862 - __main__ - INFO - 处理supplier: 100131
2025-09-19 16:16:47,863 - __main__ - WARNING - 未找到对应的vendor，跳过: 100131
2025-09-19 16:16:47,863 - __main__ - INFO - 进度: 133/215
2025-09-19 16:16:47,863 - __main__ - INFO - 处理supplier: 100132
2025-09-19 16:16:47,863 - __main__ - WARNING - 未找到对应的vendor，跳过: 100132
2025-09-19 16:16:47,863 - __main__ - INFO - 进度: 134/215
2025-09-19 16:16:47,863 - __main__ - INFO - 处理supplier: 100133
2025-09-19 16:16:47,864 - __main__ - WARNING - 未找到对应的vendor，跳过: 100133
2025-09-19 16:16:47,864 - __main__ - INFO - 进度: 135/215
2025-09-19 16:16:47,864 - __main__ - INFO - 处理supplier: 100134
2025-09-19 16:16:47,864 - __main__ - WARNING - 未找到对应的vendor，跳过: 100134
2025-09-19 16:16:47,864 - __main__ - INFO - 进度: 136/215
2025-09-19 16:16:47,864 - __main__ - INFO - 处理supplier: 100135
2025-09-19 16:16:47,865 - __main__ - WARNING - 未找到对应的vendor，跳过: 100135
2025-09-19 16:16:47,865 - __main__ - INFO - 进度: 137/215
2025-09-19 16:16:47,865 - __main__ - INFO - 处理supplier: 100136
2025-09-19 16:16:47,865 - __main__ - WARNING - 未找到对应的vendor，跳过: 100136
2025-09-19 16:16:47,865 - __main__ - INFO - 进度: 138/215
2025-09-19 16:16:47,865 - __main__ - INFO - 处理supplier: 100137
2025-09-19 16:16:47,866 - __main__ - INFO - 找到对应vendor: fcf5cfc1-6706-408a-897e-120815c1e0bb
2025-09-19 16:16:47,866 - database_manager - INFO - 成功更新vendor: fcf5cfc1-6706-408a-897e-120815c1e0bb
2025-09-19 16:16:47,867 - __main__ - INFO - 成功更新vendor: fcf5cfc1-6706-408a-897e-120815c1e0bb
2025-09-19 16:16:47,868 - database_manager - INFO - 成功删除vendor联系方式: fcf5cfc1-6706-408a-897e-120815c1e0bb
2025-09-19 16:16:47,870 - database_manager - ERROR - 插入网址失败: (psycopg2.errors.NotNullViolation) null value in column "web_address_type" of relation "web_address" violates not-null constraint
DETAIL:  Failing row contains (273a9367-53b4-4b05-aa83-a30cb863bb61, VENDOR, fcf5cfc1-6706-408a-897e-120815c1e0bb, null, <EMAIL>, 2025-09-19 16:16:47.870364, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO web_address (
                id, entity_type, entity_id, web_address_type, web_address,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(web_address_type)s, %(web_address)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '273a9367-53b4-4b05-aa83-a30cb863bb61', 'entity_type': 'VENDOR', 'entity_id': UUID('fcf5cfc1-6706-408a-897e-120815c1e0bb'), 'web_address_type': None, 'web_address': '<EMAIL>', 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 870364), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,871 - __main__ - ERROR - 插入网址失败: fcf5cfc1-6706-408a-897e-120815c1e0bb
2025-09-19 16:16:47,871 - __main__ - INFO - 成功处理supplier: 100137
2025-09-19 16:16:47,871 - __main__ - INFO - 进度: 139/215
2025-09-19 16:16:47,871 - __main__ - INFO - 处理supplier: 100138
2025-09-19 16:16:47,871 - __main__ - WARNING - 未找到对应的vendor，跳过: 100138
2025-09-19 16:16:47,871 - __main__ - INFO - 进度: 140/215
2025-09-19 16:16:47,871 - __main__ - INFO - 处理supplier: 100139
2025-09-19 16:16:47,872 - __main__ - WARNING - 未找到对应的vendor，跳过: 100139
2025-09-19 16:16:47,872 - __main__ - INFO - 进度: 141/215
2025-09-19 16:16:47,872 - __main__ - INFO - 处理supplier: 100140
2025-09-19 16:16:47,872 - __main__ - WARNING - 未找到对应的vendor，跳过: 100140
2025-09-19 16:16:47,872 - __main__ - INFO - 进度: 142/215
2025-09-19 16:16:47,872 - __main__ - INFO - 处理supplier: 100141
2025-09-19 16:16:47,873 - __main__ - WARNING - 未找到对应的vendor，跳过: 100141
2025-09-19 16:16:47,873 - __main__ - INFO - 进度: 143/215
2025-09-19 16:16:47,873 - __main__ - INFO - 处理supplier: 100142
2025-09-19 16:16:47,873 - __main__ - WARNING - 未找到对应的vendor，跳过: 100142
2025-09-19 16:16:47,873 - __main__ - INFO - 进度: 144/215
2025-09-19 16:16:47,873 - __main__ - INFO - 处理supplier: 100143
2025-09-19 16:16:47,874 - __main__ - WARNING - 未找到对应的vendor，跳过: 100143
2025-09-19 16:16:47,874 - __main__ - INFO - 进度: 145/215
2025-09-19 16:16:47,874 - __main__ - INFO - 处理supplier: 100144
2025-09-19 16:16:47,874 - __main__ - WARNING - 未找到对应的vendor，跳过: 100144
2025-09-19 16:16:47,874 - __main__ - INFO - 进度: 146/215
2025-09-19 16:16:47,874 - __main__ - INFO - 处理supplier: 100145
2025-09-19 16:16:47,875 - __main__ - WARNING - 未找到对应的vendor，跳过: 100145
2025-09-19 16:16:47,875 - __main__ - INFO - 进度: 147/215
2025-09-19 16:16:47,875 - __main__ - INFO - 处理supplier: 100146
2025-09-19 16:16:47,875 - __main__ - INFO - 找到对应vendor: ca065074-115c-4880-a487-e9df68a60c03
2025-09-19 16:16:47,876 - database_manager - INFO - 成功删除vendor联系方式: ca065074-115c-4880-a487-e9df68a60c03
2025-09-19 16:16:47,877 - __main__ - INFO - 成功处理supplier: 100146
2025-09-19 16:16:47,877 - __main__ - INFO - 进度: 148/215
2025-09-19 16:16:47,877 - __main__ - INFO - 处理supplier: 100147
2025-09-19 16:16:47,877 - __main__ - WARNING - 未找到对应的vendor，跳过: 100147
2025-09-19 16:16:47,878 - __main__ - INFO - 进度: 149/215
2025-09-19 16:16:47,878 - __main__ - INFO - 处理supplier: 100148
2025-09-19 16:16:47,878 - __main__ - INFO - 找到对应vendor: c3606020-01ea-45b0-b8f9-1d81055fcee5
2025-09-19 16:16:47,879 - database_manager - INFO - 成功更新vendor: c3606020-01ea-45b0-b8f9-1d81055fcee5
2025-09-19 16:16:47,879 - __main__ - INFO - 成功更新vendor: c3606020-01ea-45b0-b8f9-1d81055fcee5
2025-09-19 16:16:47,880 - database_manager - INFO - 成功删除vendor联系方式: c3606020-01ea-45b0-b8f9-1d81055fcee5
2025-09-19 16:16:47,881 - __main__ - INFO - 成功处理supplier: 100148
2025-09-19 16:16:47,881 - __main__ - INFO - 进度: 150/215
2025-09-19 16:16:47,881 - __main__ - INFO - 处理supplier: 100149
2025-09-19 16:16:47,882 - __main__ - INFO - 找到对应vendor: 750a7abd-6021-4a0f-ab55-5dc19670890a
2025-09-19 16:16:47,882 - database_manager - INFO - 成功更新vendor: 750a7abd-6021-4a0f-ab55-5dc19670890a
2025-09-19 16:16:47,883 - __main__ - INFO - 成功更新vendor: 750a7abd-6021-4a0f-ab55-5dc19670890a
2025-09-19 16:16:47,884 - database_manager - INFO - 成功删除vendor联系方式: 750a7abd-6021-4a0f-ab55-5dc19670890a
2025-09-19 16:16:47,888 - __main__ - INFO - 成功处理supplier: 100149
2025-09-19 16:16:47,888 - __main__ - INFO - 进度: 151/215
2025-09-19 16:16:47,888 - __main__ - INFO - 处理supplier: 100150
2025-09-19 16:16:47,889 - __main__ - INFO - 找到对应vendor: 8d319517-f804-4c68-a482-1830005dcdb2
2025-09-19 16:16:47,889 - database_manager - INFO - 成功删除vendor联系方式: 8d319517-f804-4c68-a482-1830005dcdb2
2025-09-19 16:16:47,890 - __main__ - INFO - 成功处理supplier: 100150
2025-09-19 16:16:47,890 - __main__ - INFO - 进度: 152/215
2025-09-19 16:16:47,890 - __main__ - INFO - 处理supplier: 100151
2025-09-19 16:16:47,891 - __main__ - WARNING - 未找到对应的vendor，跳过: 100151
2025-09-19 16:16:47,891 - __main__ - INFO - 进度: 153/215
2025-09-19 16:16:47,891 - __main__ - INFO - 处理supplier: 100152
2025-09-19 16:16:47,891 - __main__ - INFO - 找到对应vendor: 9ee096e0-1600-4051-afce-d9a84e431c73
2025-09-19 16:16:47,892 - database_manager - INFO - 成功更新vendor: 9ee096e0-1600-4051-afce-d9a84e431c73
2025-09-19 16:16:47,892 - __main__ - INFO - 成功更新vendor: 9ee096e0-1600-4051-afce-d9a84e431c73
2025-09-19 16:16:47,893 - database_manager - INFO - 成功删除vendor联系方式: 9ee096e0-1600-4051-afce-d9a84e431c73
2025-09-19 16:16:47,893 - __main__ - INFO - 成功处理supplier: 100152
2025-09-19 16:16:47,893 - __main__ - INFO - 进度: 154/215
2025-09-19 16:16:47,893 - __main__ - INFO - 处理supplier: 100153
2025-09-19 16:16:47,894 - __main__ - INFO - 找到对应vendor: 270eca61-fc1b-4282-9f09-c6a3a9a5bf22
2025-09-19 16:16:47,894 - database_manager - INFO - 成功更新vendor: 270eca61-fc1b-4282-9f09-c6a3a9a5bf22
2025-09-19 16:16:47,895 - __main__ - INFO - 成功更新vendor: 270eca61-fc1b-4282-9f09-c6a3a9a5bf22
2025-09-19 16:16:47,896 - database_manager - INFO - 成功删除vendor联系方式: 270eca61-fc1b-4282-9f09-c6a3a9a5bf22
2025-09-19 16:16:47,898 - __main__ - INFO - 成功处理supplier: 100153
2025-09-19 16:16:47,898 - __main__ - INFO - 进度: 155/215
2025-09-19 16:16:47,898 - __main__ - INFO - 处理supplier: 100154
2025-09-19 16:16:47,898 - __main__ - INFO - 找到对应vendor: c2cd61a5-9894-40af-8e21-b9aefb8f20d4
2025-09-19 16:16:47,899 - database_manager - INFO - 成功删除vendor联系方式: c2cd61a5-9894-40af-8e21-b9aefb8f20d4
2025-09-19 16:16:47,900 - __main__ - INFO - 成功处理supplier: 100154
2025-09-19 16:16:47,900 - __main__ - INFO - 进度: 156/215
2025-09-19 16:16:47,900 - __main__ - INFO - 处理supplier: 100155
2025-09-19 16:16:47,900 - __main__ - WARNING - 未找到对应的vendor，跳过: 100155
2025-09-19 16:16:47,900 - __main__ - INFO - 进度: 157/215
2025-09-19 16:16:47,901 - __main__ - INFO - 处理supplier: 100156
2025-09-19 16:16:47,901 - __main__ - INFO - 找到对应vendor: 420e3688-81bd-4d77-8b9d-d4733fa1da55
2025-09-19 16:16:47,902 - database_manager - INFO - 成功更新vendor: 420e3688-81bd-4d77-8b9d-d4733fa1da55
2025-09-19 16:16:47,902 - __main__ - INFO - 成功更新vendor: 420e3688-81bd-4d77-8b9d-d4733fa1da55
2025-09-19 16:16:47,903 - database_manager - INFO - 成功删除vendor联系方式: 420e3688-81bd-4d77-8b9d-d4733fa1da55
2025-09-19 16:16:47,907 - __main__ - INFO - 成功处理supplier: 100156
2025-09-19 16:16:47,907 - __main__ - INFO - 进度: 158/215
2025-09-19 16:16:47,907 - __main__ - INFO - 处理supplier: 100157
2025-09-19 16:16:47,908 - __main__ - WARNING - 未找到对应的vendor，跳过: 100157
2025-09-19 16:16:47,908 - __main__ - INFO - 进度: 159/215
2025-09-19 16:16:47,909 - __main__ - INFO - 处理supplier: 100158
2025-09-19 16:16:47,910 - __main__ - INFO - 找到对应vendor: ce8d2a5e-ec22-4d6f-9e07-eb59e811ee0f
2025-09-19 16:16:47,912 - database_manager - INFO - 成功删除vendor联系方式: ce8d2a5e-ec22-4d6f-9e07-eb59e811ee0f
2025-09-19 16:16:47,913 - __main__ - INFO - 成功处理supplier: 100158
2025-09-19 16:16:47,913 - __main__ - INFO - 进度: 160/215
2025-09-19 16:16:47,913 - __main__ - INFO - 处理supplier: 100159
2025-09-19 16:16:47,914 - __main__ - WARNING - 未找到对应的vendor，跳过: 100159
2025-09-19 16:16:47,914 - __main__ - INFO - 进度: 161/215
2025-09-19 16:16:47,914 - __main__ - INFO - 处理supplier: 100160
2025-09-19 16:16:47,915 - __main__ - INFO - 找到对应vendor: 257a30a3-bdc6-44d5-b689-e5dacbcb5e6a
2025-09-19 16:16:47,915 - database_manager - INFO - 成功更新vendor: 257a30a3-bdc6-44d5-b689-e5dacbcb5e6a
2025-09-19 16:16:47,916 - __main__ - INFO - 成功更新vendor: 257a30a3-bdc6-44d5-b689-e5dacbcb5e6a
2025-09-19 16:16:47,917 - database_manager - INFO - 成功删除vendor联系方式: 257a30a3-bdc6-44d5-b689-e5dacbcb5e6a
2025-09-19 16:16:47,921 - __main__ - INFO - 成功处理supplier: 100160
2025-09-19 16:16:47,921 - __main__ - INFO - 进度: 162/215
2025-09-19 16:16:47,921 - __main__ - INFO - 处理supplier: 100161
2025-09-19 16:16:47,921 - __main__ - INFO - 找到对应vendor: f88ef61f-c8da-42d2-8ff9-fb22b5277e54
2025-09-19 16:16:47,921 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 16:16:47,922 - database_manager - INFO - 成功更新vendor: f88ef61f-c8da-42d2-8ff9-fb22b5277e54
2025-09-19 16:16:47,922 - __main__ - INFO - 成功更新vendor: f88ef61f-c8da-42d2-8ff9-fb22b5277e54
2025-09-19 16:16:47,924 - database_manager - INFO - 成功删除vendor联系方式: f88ef61f-c8da-42d2-8ff9-fb22b5277e54
2025-09-19 16:16:47,927 - __main__ - INFO - 成功处理supplier: 100161
2025-09-19 16:16:47,927 - __main__ - INFO - 进度: 163/215
2025-09-19 16:16:47,927 - __main__ - INFO - 处理supplier: 100162
2025-09-19 16:16:47,928 - __main__ - WARNING - 未找到对应的vendor，跳过: 100162
2025-09-19 16:16:47,928 - __main__ - INFO - 进度: 164/215
2025-09-19 16:16:47,928 - __main__ - INFO - 处理supplier: 100163
2025-09-19 16:16:47,929 - __main__ - WARNING - 未找到对应的vendor，跳过: 100163
2025-09-19 16:16:47,929 - __main__ - INFO - 进度: 165/215
2025-09-19 16:16:47,929 - __main__ - INFO - 处理supplier: 100164
2025-09-19 16:16:47,930 - __main__ - WARNING - 未找到对应的vendor，跳过: 100164
2025-09-19 16:16:47,930 - __main__ - INFO - 进度: 166/215
2025-09-19 16:16:47,930 - __main__ - INFO - 处理supplier: 100165
2025-09-19 16:16:47,930 - __main__ - WARNING - 未找到对应的vendor，跳过: 100165
2025-09-19 16:16:47,930 - __main__ - INFO - 进度: 167/215
2025-09-19 16:16:47,930 - __main__ - INFO - 处理supplier: 100166
2025-09-19 16:16:47,931 - __main__ - WARNING - 未找到对应的vendor，跳过: 100166
2025-09-19 16:16:47,931 - __main__ - INFO - 进度: 168/215
2025-09-19 16:16:47,931 - __main__ - INFO - 处理supplier: 100167
2025-09-19 16:16:47,932 - __main__ - WARNING - 未找到对应的vendor，跳过: 100167
2025-09-19 16:16:47,932 - __main__ - INFO - 进度: 169/215
2025-09-19 16:16:47,932 - __main__ - INFO - 处理supplier: 100168
2025-09-19 16:16:47,933 - __main__ - WARNING - 未找到对应的vendor，跳过: 100168
2025-09-19 16:16:47,933 - __main__ - INFO - 进度: 170/215
2025-09-19 16:16:47,933 - __main__ - INFO - 处理supplier: 100169
2025-09-19 16:16:47,933 - __main__ - INFO - 找到对应vendor: 33610208-e0a2-4ff2-8ca7-79415d2cdfca
2025-09-19 16:16:47,935 - database_manager - INFO - 成功删除vendor联系方式: 33610208-e0a2-4ff2-8ca7-79415d2cdfca
2025-09-19 16:16:47,935 - __main__ - INFO - 成功处理supplier: 100169
2025-09-19 16:16:47,935 - __main__ - INFO - 进度: 171/215
2025-09-19 16:16:47,935 - __main__ - INFO - 处理supplier: 100170
2025-09-19 16:16:47,936 - __main__ - WARNING - 未找到对应的vendor，跳过: 100170
2025-09-19 16:16:47,936 - __main__ - INFO - 进度: 172/215
2025-09-19 16:16:47,936 - __main__ - INFO - 处理supplier: 100171
2025-09-19 16:16:47,937 - __main__ - WARNING - 未找到对应的vendor，跳过: 100171
2025-09-19 16:16:47,937 - __main__ - INFO - 进度: 173/215
2025-09-19 16:16:47,937 - __main__ - INFO - 处理supplier: 100172
2025-09-19 16:16:47,937 - __main__ - WARNING - 未找到对应的vendor，跳过: 100172
2025-09-19 16:16:47,937 - __main__ - INFO - 进度: 174/215
2025-09-19 16:16:47,937 - __main__ - INFO - 处理supplier: 100173
2025-09-19 16:16:47,938 - __main__ - INFO - 找到对应vendor: fd7b77bf-f417-41db-8ff7-0164327f6b4d
2025-09-19 16:16:47,939 - database_manager - INFO - 成功更新vendor: fd7b77bf-f417-41db-8ff7-0164327f6b4d
2025-09-19 16:16:47,939 - __main__ - INFO - 成功更新vendor: fd7b77bf-f417-41db-8ff7-0164327f6b4d
2025-09-19 16:16:47,940 - database_manager - INFO - 成功删除vendor联系方式: fd7b77bf-f417-41db-8ff7-0164327f6b4d
2025-09-19 16:16:47,947 - __main__ - INFO - 成功处理supplier: 100173
2025-09-19 16:16:47,947 - __main__ - INFO - 进度: 175/215
2025-09-19 16:16:47,947 - __main__ - INFO - 处理supplier: 100174
2025-09-19 16:16:47,948 - __main__ - INFO - 找到对应vendor: 2b8a46bd-9bdd-4723-a2a4-2dffa8dfc20c
2025-09-19 16:16:47,949 - database_manager - INFO - 成功删除vendor联系方式: 2b8a46bd-9bdd-4723-a2a4-2dffa8dfc20c
2025-09-19 16:16:47,949 - __main__ - INFO - 成功处理supplier: 100174
2025-09-19 16:16:47,950 - __main__ - INFO - 进度: 176/215
2025-09-19 16:16:47,950 - __main__ - INFO - 处理supplier: 100175
2025-09-19 16:16:47,950 - __main__ - INFO - 找到对应vendor: 50c427a9-e5d3-4742-8fad-7b8d2ade0a86
2025-09-19 16:16:47,951 - database_manager - INFO - 成功删除vendor联系方式: 50c427a9-e5d3-4742-8fad-7b8d2ade0a86
2025-09-19 16:16:47,952 - __main__ - INFO - 成功处理supplier: 100175
2025-09-19 16:16:47,952 - __main__ - INFO - 进度: 177/215
2025-09-19 16:16:47,952 - __main__ - INFO - 处理supplier: 100176
2025-09-19 16:16:47,953 - __main__ - INFO - 找到对应vendor: 22d09107-ca3e-4364-aa39-c6a48c1c4220
2025-09-19 16:16:47,953 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 16:16:47,953 - database_manager - INFO - 成功更新vendor: 22d09107-ca3e-4364-aa39-c6a48c1c4220
2025-09-19 16:16:47,953 - __main__ - INFO - 成功更新vendor: 22d09107-ca3e-4364-aa39-c6a48c1c4220
2025-09-19 16:16:47,954 - database_manager - INFO - 成功删除vendor联系方式: 22d09107-ca3e-4364-aa39-c6a48c1c4220
2025-09-19 16:16:47,956 - __main__ - INFO - 成功处理supplier: 100176
2025-09-19 16:16:47,956 - __main__ - INFO - 进度: 178/215
2025-09-19 16:16:47,956 - __main__ - INFO - 处理supplier: 100177
2025-09-19 16:16:47,957 - __main__ - INFO - 找到对应vendor: 0aa47e1f-8f30-4256-9de9-f302e700bc1d
2025-09-19 16:16:47,958 - database_manager - INFO - 成功更新vendor: 0aa47e1f-8f30-4256-9de9-f302e700bc1d
2025-09-19 16:16:47,958 - __main__ - INFO - 成功更新vendor: 0aa47e1f-8f30-4256-9de9-f302e700bc1d
2025-09-19 16:16:47,959 - database_manager - INFO - 成功删除vendor联系方式: 0aa47e1f-8f30-4256-9de9-f302e700bc1d
2025-09-19 16:16:47,960 - __main__ - INFO - 成功处理supplier: 100177
2025-09-19 16:16:47,960 - __main__ - INFO - 进度: 179/215
2025-09-19 16:16:47,960 - __main__ - INFO - 处理supplier: 100178
2025-09-19 16:16:47,960 - __main__ - WARNING - 未找到对应的vendor，跳过: 100178
2025-09-19 16:16:47,960 - __main__ - INFO - 进度: 180/215
2025-09-19 16:16:47,960 - __main__ - INFO - 处理supplier: 100179
2025-09-19 16:16:47,961 - __main__ - WARNING - 未找到对应的vendor，跳过: 100179
2025-09-19 16:16:47,961 - __main__ - INFO - 进度: 181/215
2025-09-19 16:16:47,961 - __main__ - INFO - 处理supplier: 100180
2025-09-19 16:16:47,962 - __main__ - WARNING - 未找到对应的vendor，跳过: 100180
2025-09-19 16:16:47,962 - __main__ - INFO - 进度: 182/215
2025-09-19 16:16:47,962 - __main__ - INFO - 处理supplier: 100181
2025-09-19 16:16:47,962 - __main__ - INFO - 找到对应vendor: 932a5b43-3abd-4186-91f3-5a2c571aaafb
2025-09-19 16:16:47,963 - database_manager - INFO - 成功更新vendor: 932a5b43-3abd-4186-91f3-5a2c571aaafb
2025-09-19 16:16:47,963 - __main__ - INFO - 成功更新vendor: 932a5b43-3abd-4186-91f3-5a2c571aaafb
2025-09-19 16:16:47,964 - database_manager - INFO - 成功删除vendor联系方式: 932a5b43-3abd-4186-91f3-5a2c571aaafb
2025-09-19 16:16:47,965 - database_manager - ERROR - 插入地址失败: (psycopg2.errors.NotNullViolation) null value in column "purpose" of relation "address" violates not-null constraint
DETAIL:  Failing row contains (816223d3-c007-43f2-9ab0-c0b5271d3061, VENDOR, 932a5b43-3abd-4186-91f3-5a2c571aaafb, 3579 E FOOTHILL BLVD STE 501, PASADENA CA, CA, 91107, USA, null, null, null, 2025-09-19 16:16:47.965311, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO address (
                id, entity_type, entity_id, street_address, city, state, 
                postal_code, country, directions, purpose, additional_lines,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(street_address)s, %(city)s, %(state)s,
                %(postal_code)s, %(country)s, %(directions)s, %(purpose)s, %(additional_lines)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '816223d3-c007-43f2-9ab0-c0b5271d3061', 'entity_type': 'VENDOR', 'entity_id': UUID('932a5b43-3abd-4186-91f3-5a2c571aaafb'), 'street_address': '3579 E FOOTHILL BLVD STE 501', 'city': 'PASADENA CA', 'state': 'CA', 'postal_code': '91107', 'country': 'USA', 'directions': None, 'purpose': None, 'additional_lines': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 965311), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,966 - __main__ - ERROR - 插入地址失败: 932a5b43-3abd-4186-91f3-5a2c571aaafb
2025-09-19 16:16:47,966 - database_manager - ERROR - 插入邮箱失败: (psycopg2.errors.NotNullViolation) null value in column "email_type" of relation "email" violates not-null constraint
DETAIL:  Failing row contains (0cf22b23-a514-4b78-9817-87f4b4b71d74, VENDOR, 932a5b43-3abd-4186-91f3-5a2c571aaafb, null, <EMAIL>, null, 2025-09-19 16:16:47.966091, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO email (
                id, entity_type, entity_id, email_type, email, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(email_type)s, %(email)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '0cf22b23-a514-4b78-9817-87f4b4b71d74', 'entity_type': 'VENDOR', 'entity_id': UUID('932a5b43-3abd-4186-91f3-5a2c571aaafb'), 'email_type': None, 'email': '<EMAIL>', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 966091), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,966 - __main__ - ERROR - 插入邮箱失败: 932a5b43-3abd-4186-91f3-5a2c571aaafb
2025-09-19 16:16:47,966 - __main__ - INFO - 成功处理supplier: 100181
2025-09-19 16:16:47,966 - __main__ - INFO - 进度: 183/215
2025-09-19 16:16:47,966 - __main__ - INFO - 处理supplier: 100182
2025-09-19 16:16:47,967 - __main__ - INFO - 找到对应vendor: 28b6a6f3-1a24-48ab-9620-22c6e68f967f
2025-09-19 16:16:47,967 - database_manager - INFO - 成功更新vendor: 28b6a6f3-1a24-48ab-9620-22c6e68f967f
2025-09-19 16:16:47,968 - __main__ - INFO - 成功更新vendor: 28b6a6f3-1a24-48ab-9620-22c6e68f967f
2025-09-19 16:16:47,969 - database_manager - INFO - 成功删除vendor联系方式: 28b6a6f3-1a24-48ab-9620-22c6e68f967f
2025-09-19 16:16:47,970 - database_manager - ERROR - 插入邮箱失败: (psycopg2.errors.NotNullViolation) null value in column "email_type" of relation "email" violates not-null constraint
DETAIL:  Failing row contains (ef2914cb-95be-4578-9657-0e7535d952c2, VENDOR, 28b6a6f3-1a24-48ab-9620-22c6e68f967f, null, <EMAIL>, null, 2025-09-19 16:16:47.969969, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO email (
                id, entity_type, entity_id, email_type, email, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(email_type)s, %(email)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': 'ef2914cb-95be-4578-9657-0e7535d952c2', 'entity_type': 'VENDOR', 'entity_id': UUID('28b6a6f3-1a24-48ab-9620-22c6e68f967f'), 'email_type': None, 'email': '<EMAIL>', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 969969), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,970 - __main__ - ERROR - 插入邮箱失败: 28b6a6f3-1a24-48ab-9620-22c6e68f967f
2025-09-19 16:16:47,970 - __main__ - INFO - 成功处理supplier: 100182
2025-09-19 16:16:47,970 - __main__ - INFO - 进度: 184/215
2025-09-19 16:16:47,970 - __main__ - INFO - 处理supplier: 100183
2025-09-19 16:16:47,971 - __main__ - INFO - 找到对应vendor: d69eba4c-804f-4289-a2d5-229bf9965971
2025-09-19 16:16:47,971 - data_mapper - WARNING - 电话号码长度不符合要求，跳过
2025-09-19 16:16:47,971 - database_manager - INFO - 成功更新vendor: d69eba4c-804f-4289-a2d5-229bf9965971
2025-09-19 16:16:47,972 - __main__ - INFO - 成功更新vendor: d69eba4c-804f-4289-a2d5-229bf9965971
2025-09-19 16:16:47,973 - database_manager - INFO - 成功删除vendor联系方式: d69eba4c-804f-4289-a2d5-229bf9965971
2025-09-19 16:16:47,977 - database_manager - ERROR - 插入网址失败: (psycopg2.errors.NotNullViolation) null value in column "web_address_type" of relation "web_address" violates not-null constraint
DETAIL:  Failing row contains (8786ab8e-45c9-494a-8188-884b78730678, VENDOR, d69eba4c-804f-4289-a2d5-229bf9965971, null, www.BAZICPRODUCTS.com, 2025-09-19 16:16:47.9764, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO web_address (
                id, entity_type, entity_id, web_address_type, web_address,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(web_address_type)s, %(web_address)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '8786ab8e-45c9-494a-8188-884b78730678', 'entity_type': 'VENDOR', 'entity_id': UUID('d69eba4c-804f-4289-a2d5-229bf9965971'), 'web_address_type': None, 'web_address': 'www.BAZICPRODUCTS.com', 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 976400), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,977 - __main__ - ERROR - 插入网址失败: d69eba4c-804f-4289-a2d5-229bf9965971
2025-09-19 16:16:47,977 - __main__ - INFO - 成功处理supplier: 100183
2025-09-19 16:16:47,977 - __main__ - INFO - 进度: 185/215
2025-09-19 16:16:47,977 - __main__ - INFO - 处理supplier: 100184
2025-09-19 16:16:47,978 - __main__ - INFO - 找到对应vendor: eefd491c-ead8-432e-b3c8-eb1dab54acfc
2025-09-19 16:16:47,978 - database_manager - INFO - 成功更新vendor: eefd491c-ead8-432e-b3c8-eb1dab54acfc
2025-09-19 16:16:47,978 - __main__ - INFO - 成功更新vendor: eefd491c-ead8-432e-b3c8-eb1dab54acfc
2025-09-19 16:16:47,979 - database_manager - INFO - 成功删除vendor联系方式: eefd491c-ead8-432e-b3c8-eb1dab54acfc
2025-09-19 16:16:47,983 - database_manager - ERROR - 插入网址失败: (psycopg2.errors.NotNullViolation) null value in column "web_address_type" of relation "web_address" violates not-null constraint
DETAIL:  Failing row contains (0dbb7824-31bb-40b4-b020-17ecb7cb12ec, VENDOR, eefd491c-ead8-432e-b3c8-eb1dab54acfc, null, www.readyrefresh.com, 2025-09-19 16:16:47.983204, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO web_address (
                id, entity_type, entity_id, web_address_type, web_address,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(web_address_type)s, %(web_address)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '0dbb7824-31bb-40b4-b020-17ecb7cb12ec', 'entity_type': 'VENDOR', 'entity_id': UUID('eefd491c-ead8-432e-b3c8-eb1dab54acfc'), 'web_address_type': None, 'web_address': 'www.readyrefresh.com', 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 47, 983204), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:47,983 - __main__ - ERROR - 插入网址失败: eefd491c-ead8-432e-b3c8-eb1dab54acfc
2025-09-19 16:16:47,984 - __main__ - INFO - 成功处理supplier: 100184
2025-09-19 16:16:47,984 - __main__ - INFO - 进度: 186/215
2025-09-19 16:16:47,984 - __main__ - INFO - 处理supplier: 100185
2025-09-19 16:16:47,984 - __main__ - INFO - 找到对应vendor: d84a956f-576f-447f-b327-85ae5d76aa9f
2025-09-19 16:16:47,984 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 16:16:47,985 - database_manager - INFO - 成功更新vendor: d84a956f-576f-447f-b327-85ae5d76aa9f
2025-09-19 16:16:47,985 - __main__ - INFO - 成功更新vendor: d84a956f-576f-447f-b327-85ae5d76aa9f
2025-09-19 16:16:47,986 - database_manager - INFO - 成功删除vendor联系方式: d84a956f-576f-447f-b327-85ae5d76aa9f
2025-09-19 16:16:47,990 - __main__ - INFO - 成功处理supplier: 100185
2025-09-19 16:16:47,990 - __main__ - INFO - 进度: 187/215
2025-09-19 16:16:47,990 - __main__ - INFO - 处理supplier: 100186
2025-09-19 16:16:47,990 - __main__ - INFO - 找到对应vendor: a427db77-4706-4f6e-b3cd-ce482ebb5a86
2025-09-19 16:16:47,991 - database_manager - INFO - 成功更新vendor: a427db77-4706-4f6e-b3cd-ce482ebb5a86
2025-09-19 16:16:47,991 - __main__ - INFO - 成功更新vendor: a427db77-4706-4f6e-b3cd-ce482ebb5a86
2025-09-19 16:16:47,992 - database_manager - INFO - 成功删除vendor联系方式: a427db77-4706-4f6e-b3cd-ce482ebb5a86
2025-09-19 16:16:47,993 - __main__ - INFO - 成功处理supplier: 100186
2025-09-19 16:16:47,993 - __main__ - INFO - 进度: 188/215
2025-09-19 16:16:47,993 - __main__ - INFO - 处理supplier: 100187
2025-09-19 16:16:47,994 - __main__ - INFO - 找到对应vendor: 89f85b7d-aed3-4355-acd5-b8b55ac93f4d
2025-09-19 16:16:47,995 - database_manager - INFO - 成功删除vendor联系方式: 89f85b7d-aed3-4355-acd5-b8b55ac93f4d
2025-09-19 16:16:47,996 - __main__ - INFO - 成功处理supplier: 100187
2025-09-19 16:16:47,996 - __main__ - INFO - 进度: 189/215
2025-09-19 16:16:47,996 - __main__ - INFO - 处理supplier: 100188
2025-09-19 16:16:47,996 - __main__ - INFO - 找到对应vendor: 7456d2ca-ee61-4c06-ab37-45579958f001
2025-09-19 16:16:47,997 - database_manager - INFO - 成功删除vendor联系方式: 7456d2ca-ee61-4c06-ab37-45579958f001
2025-09-19 16:16:47,998 - __main__ - INFO - 成功处理supplier: 100188
2025-09-19 16:16:47,999 - __main__ - INFO - 进度: 190/215
2025-09-19 16:16:47,999 - __main__ - INFO - 处理supplier: 100189
2025-09-19 16:16:47,999 - __main__ - INFO - 找到对应vendor: faa4a421-3239-44c7-8626-bb89b9614341
2025-09-19 16:16:48,000 - database_manager - INFO - 成功更新vendor: faa4a421-3239-44c7-8626-bb89b9614341
2025-09-19 16:16:48,000 - __main__ - INFO - 成功更新vendor: faa4a421-3239-44c7-8626-bb89b9614341
2025-09-19 16:16:48,002 - database_manager - INFO - 成功删除vendor联系方式: faa4a421-3239-44c7-8626-bb89b9614341
2025-09-19 16:16:48,004 - __main__ - INFO - 成功处理supplier: 100189
2025-09-19 16:16:48,004 - __main__ - INFO - 进度: 191/215
2025-09-19 16:16:48,004 - __main__ - INFO - 处理supplier: 100190
2025-09-19 16:16:48,005 - __main__ - INFO - 找到对应vendor: da3cbc5a-3035-4d2b-8e5a-de3056d8f8fc
2025-09-19 16:16:48,006 - database_manager - INFO - 成功删除vendor联系方式: da3cbc5a-3035-4d2b-8e5a-de3056d8f8fc
2025-09-19 16:16:48,007 - __main__ - INFO - 成功处理supplier: 100190
2025-09-19 16:16:48,007 - __main__ - INFO - 进度: 192/215
2025-09-19 16:16:48,007 - __main__ - INFO - 处理supplier: 100191
2025-09-19 16:16:48,009 - __main__ - INFO - 找到对应vendor: 025ea634-8a99-4b78-a47a-e1924a30ac53
2025-09-19 16:16:48,009 - database_manager - INFO - 成功更新vendor: 025ea634-8a99-4b78-a47a-e1924a30ac53
2025-09-19 16:16:48,010 - __main__ - INFO - 成功更新vendor: 025ea634-8a99-4b78-a47a-e1924a30ac53
2025-09-19 16:16:48,013 - database_manager - INFO - 成功删除vendor联系方式: 025ea634-8a99-4b78-a47a-e1924a30ac53
2025-09-19 16:16:48,015 - __main__ - INFO - 成功处理supplier: 100191
2025-09-19 16:16:48,015 - __main__ - INFO - 进度: 193/215
2025-09-19 16:16:48,015 - __main__ - INFO - 处理supplier: 100192
2025-09-19 16:16:48,017 - __main__ - INFO - 找到对应vendor: ab86a05e-dcb1-4cd3-a8c4-99f542a36cd8
2025-09-19 16:16:48,017 - database_manager - INFO - 成功更新vendor: ab86a05e-dcb1-4cd3-a8c4-99f542a36cd8
2025-09-19 16:16:48,018 - __main__ - INFO - 成功更新vendor: ab86a05e-dcb1-4cd3-a8c4-99f542a36cd8
2025-09-19 16:16:48,020 - database_manager - INFO - 成功删除vendor联系方式: ab86a05e-dcb1-4cd3-a8c4-99f542a36cd8
2025-09-19 16:16:48,023 - __main__ - INFO - 成功处理supplier: 100192
2025-09-19 16:16:48,023 - __main__ - INFO - 进度: 194/215
2025-09-19 16:16:48,023 - __main__ - INFO - 处理supplier: 100193
2025-09-19 16:16:48,025 - __main__ - INFO - 找到对应vendor: 4a35b4ce-4c07-4aff-b992-33c5eca975a9
2025-09-19 16:16:48,026 - database_manager - INFO - 成功删除vendor联系方式: 4a35b4ce-4c07-4aff-b992-33c5eca975a9
2025-09-19 16:16:48,028 - __main__ - INFO - 成功处理supplier: 100193
2025-09-19 16:16:48,028 - __main__ - INFO - 进度: 195/215
2025-09-19 16:16:48,028 - __main__ - INFO - 处理supplier: 100194
2025-09-19 16:16:48,030 - __main__ - INFO - 找到对应vendor: a9a6ae9d-f24e-4997-ac5e-2c5f06f623f2
2025-09-19 16:16:48,031 - database_manager - INFO - 成功更新vendor: a9a6ae9d-f24e-4997-ac5e-2c5f06f623f2
2025-09-19 16:16:48,031 - __main__ - INFO - 成功更新vendor: a9a6ae9d-f24e-4997-ac5e-2c5f06f623f2
2025-09-19 16:16:48,033 - database_manager - INFO - 成功删除vendor联系方式: a9a6ae9d-f24e-4997-ac5e-2c5f06f623f2
2025-09-19 16:16:48,035 - database_manager - ERROR - 插入地址失败: (psycopg2.errors.NotNullViolation) null value in column "purpose" of relation "address" violates not-null constraint
DETAIL:  Failing row contains (0da06771-73ef-4085-b7ba-a196a56c7120, VENDOR, a9a6ae9d-f24e-4997-ac5e-2c5f06f623f2, 4625 S. Alameda st., Vernon, CA, 90058, USA, null, null, null, 2025-09-19 16:16:48.034413, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO address (
                id, entity_type, entity_id, street_address, city, state, 
                postal_code, country, directions, purpose, additional_lines,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(street_address)s, %(city)s, %(state)s,
                %(postal_code)s, %(country)s, %(directions)s, %(purpose)s, %(additional_lines)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '0da06771-73ef-4085-b7ba-a196a56c7120', 'entity_type': 'VENDOR', 'entity_id': UUID('a9a6ae9d-f24e-4997-ac5e-2c5f06f623f2'), 'street_address': '4625 S. Alameda st.', 'city': 'Vernon', 'state': 'CA', 'postal_code': '90058', 'country': 'USA', 'directions': None, 'purpose': None, 'additional_lines': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 48, 34413), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:48,035 - __main__ - ERROR - 插入地址失败: a9a6ae9d-f24e-4997-ac5e-2c5f06f623f2
2025-09-19 16:16:48,037 - database_manager - ERROR - 插入电话失败: (psycopg2.errors.NotNullViolation) null value in column "phone_type" of relation "phone_number" violates not-null constraint
DETAIL:  Failing row contains (9f0c3cf5-708b-4951-a208-d78d604e87c4, VENDOR, a9a6ae9d-f24e-4997-ac5e-2c5f06f623f2, null, 213)514-4243, null, 2025-09-19 16:16:48.035963, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO phone_number (
                id, entity_type, entity_id, phone_type, phone_number, extension,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(phone_type)s, %(phone_number)s, %(extension)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '9f0c3cf5-708b-4951-a208-d78d604e87c4', 'entity_type': 'VENDOR', 'entity_id': UUID('a9a6ae9d-f24e-4997-ac5e-2c5f06f623f2'), 'phone_type': None, 'phone_number': '213)514-4243', 'extension': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 48, 35963), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:48,037 - __main__ - ERROR - 插入电话失败: a9a6ae9d-f24e-4997-ac5e-2c5f06f623f2
2025-09-19 16:16:48,037 - __main__ - INFO - 成功处理supplier: 100194
2025-09-19 16:16:48,037 - __main__ - INFO - 进度: 196/215
2025-09-19 16:16:48,037 - __main__ - INFO - 处理supplier: 100195
2025-09-19 16:16:48,039 - __main__ - WARNING - 未找到对应的vendor，跳过: 100195
2025-09-19 16:16:48,039 - __main__ - INFO - 进度: 197/215
2025-09-19 16:16:48,039 - __main__ - INFO - 处理supplier: 100196
2025-09-19 16:16:48,040 - __main__ - WARNING - 未找到对应的vendor，跳过: 100196
2025-09-19 16:16:48,040 - __main__ - INFO - 进度: 198/215
2025-09-19 16:16:48,040 - __main__ - INFO - 处理supplier: 100197
2025-09-19 16:16:48,042 - __main__ - INFO - 找到对应vendor: fcb471c3-95f3-4813-a719-8055482986c1
2025-09-19 16:16:48,043 - database_manager - INFO - 成功更新vendor: fcb471c3-95f3-4813-a719-8055482986c1
2025-09-19 16:16:48,044 - __main__ - INFO - 成功更新vendor: fcb471c3-95f3-4813-a719-8055482986c1
2025-09-19 16:16:48,046 - database_manager - INFO - 成功删除vendor联系方式: fcb471c3-95f3-4813-a719-8055482986c1
2025-09-19 16:16:48,057 - __main__ - INFO - 成功处理supplier: 100197
2025-09-19 16:16:48,057 - __main__ - INFO - 进度: 199/215
2025-09-19 16:16:48,057 - __main__ - INFO - 处理supplier: 100198
2025-09-19 16:16:48,058 - __main__ - INFO - 找到对应vendor: 993aa272-f026-493e-a914-8058b984048e
2025-09-19 16:16:48,059 - database_manager - INFO - 成功更新vendor: 993aa272-f026-493e-a914-8058b984048e
2025-09-19 16:16:48,059 - __main__ - INFO - 成功更新vendor: 993aa272-f026-493e-a914-8058b984048e
2025-09-19 16:16:48,061 - database_manager - INFO - 成功删除vendor联系方式: 993aa272-f026-493e-a914-8058b984048e
2025-09-19 16:16:48,062 - __main__ - INFO - 成功处理supplier: 100198
2025-09-19 16:16:48,062 - __main__ - INFO - 进度: 200/215
2025-09-19 16:16:48,062 - __main__ - INFO - 处理supplier: 100199
2025-09-19 16:16:48,063 - __main__ - INFO - 找到对应vendor: 33c4b05d-c874-4833-a063-7284ff28bc00
2025-09-19 16:16:48,064 - database_manager - INFO - 成功更新vendor: 33c4b05d-c874-4833-a063-7284ff28bc00
2025-09-19 16:16:48,064 - __main__ - INFO - 成功更新vendor: 33c4b05d-c874-4833-a063-7284ff28bc00
2025-09-19 16:16:48,065 - database_manager - INFO - 成功删除vendor联系方式: 33c4b05d-c874-4833-a063-7284ff28bc00
2025-09-19 16:16:48,066 - __main__ - INFO - 成功处理supplier: 100199
2025-09-19 16:16:48,066 - __main__ - INFO - 进度: 201/215
2025-09-19 16:16:48,066 - __main__ - INFO - 处理supplier: 100200
2025-09-19 16:16:48,067 - __main__ - INFO - 找到对应vendor: 22b61b5d-4819-4be6-b4bf-7c52813c7670
2025-09-19 16:16:48,068 - database_manager - INFO - 成功删除vendor联系方式: 22b61b5d-4819-4be6-b4bf-7c52813c7670
2025-09-19 16:16:48,069 - __main__ - INFO - 成功处理supplier: 100200
2025-09-19 16:16:48,069 - __main__ - INFO - 进度: 202/215
2025-09-19 16:16:48,069 - __main__ - INFO - 处理supplier: 100201
2025-09-19 16:16:48,069 - __main__ - INFO - 找到对应vendor: 492eda25-4b9a-4a01-8497-a62ba42a05fa
2025-09-19 16:16:48,069 - data_mapper - WARNING - 地址信息不完整，跳过
2025-09-19 16:16:48,070 - database_manager - INFO - 成功更新vendor: 492eda25-4b9a-4a01-8497-a62ba42a05fa
2025-09-19 16:16:48,070 - __main__ - INFO - 成功更新vendor: 492eda25-4b9a-4a01-8497-a62ba42a05fa
2025-09-19 16:16:48,071 - database_manager - INFO - 成功删除vendor联系方式: 492eda25-4b9a-4a01-8497-a62ba42a05fa
2025-09-19 16:16:48,073 - __main__ - INFO - 成功处理supplier: 100201
2025-09-19 16:16:48,073 - __main__ - INFO - 进度: 203/215
2025-09-19 16:16:48,073 - __main__ - INFO - 处理supplier: 100202
2025-09-19 16:16:48,074 - __main__ - INFO - 找到对应vendor: de26f7ec-0eaa-4071-be8b-093ac89ae959
2025-09-19 16:16:48,074 - database_manager - INFO - 成功删除vendor联系方式: de26f7ec-0eaa-4071-be8b-093ac89ae959
2025-09-19 16:16:48,075 - __main__ - INFO - 成功处理supplier: 100202
2025-09-19 16:16:48,075 - __main__ - INFO - 进度: 204/215
2025-09-19 16:16:48,075 - __main__ - INFO - 处理supplier: 100203
2025-09-19 16:16:48,076 - __main__ - INFO - 找到对应vendor: c569b030-606d-4abb-af4d-57b8ef225518
2025-09-19 16:16:48,076 - database_manager - INFO - 成功更新vendor: c569b030-606d-4abb-af4d-57b8ef225518
2025-09-19 16:16:48,076 - __main__ - INFO - 成功更新vendor: c569b030-606d-4abb-af4d-57b8ef225518
2025-09-19 16:16:48,077 - database_manager - INFO - 成功删除vendor联系方式: c569b030-606d-4abb-af4d-57b8ef225518
2025-09-19 16:16:48,079 - database_manager - ERROR - 插入地址失败: (psycopg2.errors.NotNullViolation) null value in column "purpose" of relation "address" violates not-null constraint
DETAIL:  Failing row contains (6b3d1875-110f-44f0-9000-e4fa5a2efdcb, VENDOR, c569b030-606d-4abb-af4d-57b8ef225518, 365 W 131st Street, Los Angeles, CA, 90061, USA, null, null, null, 2025-09-19 16:16:48.078612, finale_migration, Finale Migration System, null, null, null, null, null, null).

[SQL: 
            INSERT INTO address (
                id, entity_type, entity_id, street_address, city, state, 
                postal_code, country, directions, purpose, additional_lines,
                created_at, created_by, created_user_name
            ) VALUES (
                %(id)s, %(entity_type)s, %(entity_id)s, %(street_address)s, %(city)s, %(state)s,
                %(postal_code)s, %(country)s, %(directions)s, %(purpose)s, %(additional_lines)s,
                %(created_at)s, %(created_by)s, %(created_user_name)s
            )
        ]
[parameters: {'id': '6b3d1875-110f-44f0-9000-e4fa5a2efdcb', 'entity_type': 'VENDOR', 'entity_id': UUID('c569b030-606d-4abb-af4d-57b8ef225518'), 'street_address': '365 W 131st Street', 'city': 'Los Angeles', 'state': 'CA', 'postal_code': '90061', 'country': 'USA', 'directions': None, 'purpose': None, 'additional_lines': None, 'created_at': datetime.datetime(2025, 9, 19, 16, 16, 48, 78612), 'created_by': 'finale_migration', 'created_user_name': 'Finale Migration System'}]
(Background on this error at: https://sqlalche.me/e/20/gkpj)
2025-09-19 16:16:48,079 - __main__ - ERROR - 插入地址失败: c569b030-606d-4abb-af4d-57b8ef225518
2025-09-19 16:16:48,080 - __main__ - INFO - 成功处理supplier: 100203
2025-09-19 16:16:48,080 - __main__ - INFO - 进度: 205/215
2025-09-19 16:16:48,080 - __main__ - INFO - 处理supplier: 100204
2025-09-19 16:16:48,081 - __main__ - INFO - 找到对应vendor: d23836f6-c835-44be-bf18-2f47ed6fafd1
2025-09-19 16:16:48,081 - database_manager - INFO - 成功更新vendor: d23836f6-c835-44be-bf18-2f47ed6fafd1
2025-09-19 16:16:48,081 - __main__ - INFO - 成功更新vendor: d23836f6-c835-44be-bf18-2f47ed6fafd1
2025-09-19 16:16:48,082 - database_manager - INFO - 成功删除vendor联系方式: d23836f6-c835-44be-bf18-2f47ed6fafd1
2025-09-19 16:16:48,084 - __main__ - INFO - 成功处理supplier: 100204
2025-09-19 16:16:48,084 - __main__ - INFO - 进度: 206/215
2025-09-19 16:16:48,084 - __main__ - INFO - 处理supplier: 100205
2025-09-19 16:16:48,084 - __main__ - INFO - 找到对应vendor: d76624b2-8cb8-4cb1-a93b-947684f4713f
2025-09-19 16:16:48,085 - database_manager - INFO - 成功更新vendor: d76624b2-8cb8-4cb1-a93b-947684f4713f
2025-09-19 16:16:48,085 - __main__ - INFO - 成功更新vendor: d76624b2-8cb8-4cb1-a93b-947684f4713f
2025-09-19 16:16:48,086 - database_manager - INFO - 成功删除vendor联系方式: d76624b2-8cb8-4cb1-a93b-947684f4713f
2025-09-19 16:16:48,086 - __main__ - INFO - 成功处理supplier: 100205
2025-09-19 16:16:48,086 - __main__ - INFO - 进度: 207/215
2025-09-19 16:16:48,086 - __main__ - INFO - 处理supplier: 100206
2025-09-19 16:16:48,087 - __main__ - INFO - 找到对应vendor: ca65261b-02a7-48bc-b85e-c0c9a4861ac7
2025-09-19 16:16:48,087 - database_manager - INFO - 成功更新vendor: ca65261b-02a7-48bc-b85e-c0c9a4861ac7
2025-09-19 16:16:48,088 - __main__ - INFO - 成功更新vendor: ca65261b-02a7-48bc-b85e-c0c9a4861ac7
2025-09-19 16:16:48,089 - database_manager - INFO - 成功删除vendor联系方式: ca65261b-02a7-48bc-b85e-c0c9a4861ac7
2025-09-19 16:16:48,090 - __main__ - INFO - 成功处理supplier: 100206
2025-09-19 16:16:48,090 - __main__ - INFO - 进度: 208/215
2025-09-19 16:16:48,090 - __main__ - INFO - 处理supplier: 100207
2025-09-19 16:16:48,091 - __main__ - INFO - 找到对应vendor: 86adf4e5-0e2a-4039-b728-cf8eef682cda
2025-09-19 16:16:48,091 - database_manager - INFO - 成功更新vendor: 86adf4e5-0e2a-4039-b728-cf8eef682cda
2025-09-19 16:16:48,092 - __main__ - INFO - 成功更新vendor: 86adf4e5-0e2a-4039-b728-cf8eef682cda
2025-09-19 16:16:48,092 - database_manager - INFO - 成功删除vendor联系方式: 86adf4e5-0e2a-4039-b728-cf8eef682cda
2025-09-19 16:16:48,094 - __main__ - INFO - 成功处理supplier: 100207
2025-09-19 16:16:48,094 - __main__ - INFO - 进度: 209/215
2025-09-19 16:16:48,094 - __main__ - INFO - 处理supplier: 100208
2025-09-19 16:16:48,094 - __main__ - WARNING - 未找到对应的vendor，跳过: 100208
2025-09-19 16:16:48,094 - __main__ - INFO - 进度: 210/215
2025-09-19 16:16:48,094 - __main__ - INFO - 处理supplier: 100209
2025-09-19 16:16:48,095 - __main__ - WARNING - 未找到对应的vendor，跳过: 100209
2025-09-19 16:16:48,095 - __main__ - INFO - 进度: 211/215
2025-09-19 16:16:48,095 - __main__ - INFO - 处理supplier: 100210
2025-09-19 16:16:48,096 - __main__ - WARNING - 未找到对应的vendor，跳过: 100210
2025-09-19 16:16:48,096 - __main__ - INFO - 进度: 212/215
2025-09-19 16:16:48,096 - __main__ - INFO - 处理supplier: 100211
2025-09-19 16:16:48,096 - __main__ - WARNING - 未找到对应的vendor，跳过: 100211
2025-09-19 16:16:48,096 - __main__ - INFO - 进度: 213/215
2025-09-19 16:16:48,096 - __main__ - INFO - 处理supplier: 100212
2025-09-19 16:16:48,097 - __main__ - WARNING - 未找到对应的vendor，跳过: 100212
2025-09-19 16:16:48,097 - __main__ - INFO - 进度: 214/215
2025-09-19 16:16:48,097 - __main__ - INFO - 处理supplier: 100213
2025-09-19 16:16:48,097 - __main__ - INFO - 找到对应vendor: 5a5a4506-3856-4547-87e7-8f88493add4c
2025-09-19 16:16:48,098 - database_manager - INFO - 成功删除vendor联系方式: 5a5a4506-3856-4547-87e7-8f88493add4c
2025-09-19 16:16:48,099 - __main__ - INFO - 成功处理supplier: 100213
2025-09-19 16:16:48,099 - __main__ - INFO - 进度: 215/215
2025-09-19 16:16:48,099 - __main__ - INFO - 处理supplier: PRIMARY
2025-09-19 16:16:48,100 - __main__ - WARNING - 未找到对应的vendor，跳过: PRIMARY
2025-09-19 16:16:48,100 - __main__ - INFO - ==================================================
2025-09-19 16:16:48,100 - __main__ - INFO - 迁移统计信息:
2025-09-19 16:16:48,100 - __main__ - INFO - 总supplier数量: 215
2025-09-19 16:16:48,100 - __main__ - INFO - 已处理supplier: 116
2025-09-19 16:16:48,100 - __main__ - INFO - 跳过的supplier: 99
2025-09-19 16:16:48,100 - __main__ - INFO - 更新的vendor: 101
2025-09-19 16:16:48,100 - __main__ - INFO - 插入的地址: 38
2025-09-19 16:16:48,100 - __main__ - INFO - 插入的邮箱: 135
2025-09-19 16:16:48,100 - __main__ - INFO - 插入的电话: 54
2025-09-19 16:16:48,100 - __main__ - INFO - 插入的网址: 0
2025-09-19 16:16:48,100 - __main__ - INFO - 插入的额外信息: 0
2025-09-19 16:16:48,100 - __main__ - INFO - 错误数量: 0
2025-09-19 16:16:48,100 - __main__ - INFO - ==================================================
2025-09-19 16:16:48,100 - __main__ - INFO - 迁移完成，耗时: 0:01:30.920228
2025-09-19 16:43:25,367 - __main__ - INFO - 日志系统初始化完成
2025-09-19 16:43:25,368 - __main__ - INFO - 开始Finale Supplier数据迁移
2025-09-19 16:43:25,368 - __main__ - INFO - 验证连接...
2025-09-19 16:43:27,470 - __main__ - INFO - 所有连接验证成功
2025-09-19 16:43:27,470 - __main__ - INFO - 开始获取Finale supplier数据...
2025-09-19 16:43:27,470 - finale_api_client - INFO - 正在获取partygroup列表: https://app.finaleinventory.com/mercaso/api/partygroup
2025-09-19 16:43:27,995 - finale_api_client - INFO - 成功获取partygroup列表，共 215 个supplier
2025-09-19 16:43:27,995 - finale_api_client - INFO - 正在处理supplier 1/215: 100000
2025-09-19 16:43:28,489 - finale_api_client - INFO - 正在处理supplier 2/215: 100001
2025-09-19 16:43:29,217 - finale_api_client - INFO - 正在处理supplier 3/215: 100002
2025-09-19 16:43:29,618 - finale_api_client - INFO - 正在处理supplier 4/215: 100003
2025-09-19 16:43:29,991 - finale_api_client - INFO - 正在处理supplier 5/215: 100004
2025-09-19 16:43:30,325 - finale_api_client - INFO - 正在处理supplier 6/215: 100005
2025-09-19 16:43:30,755 - finale_api_client - INFO - 正在处理supplier 7/215: 100006
2025-09-19 16:43:31,480 - finale_api_client - INFO - 正在处理supplier 8/215: 100007
2025-09-19 16:43:32,136 - finale_api_client - INFO - 正在处理supplier 9/215: 100008
2025-09-19 16:43:32,521 - finale_api_client - INFO - 正在处理supplier 10/215: 100009
2025-09-19 16:43:32,916 - finale_api_client - INFO - 正在处理supplier 11/215: 100010
2025-09-19 16:43:33,343 - finale_api_client - INFO - 正在处理supplier 12/215: 100011
2025-09-19 16:43:33,738 - finale_api_client - INFO - 正在处理supplier 13/215: 100012
2025-09-19 16:43:34,242 - finale_api_client - INFO - 正在处理supplier 14/215: 100013
2025-09-19 16:43:34,634 - finale_api_client - INFO - 正在处理supplier 15/215: 100014
2025-09-19 16:43:34,977 - finale_api_client - INFO - 正在处理supplier 16/215: 100015
2025-09-19 16:43:35,372 - finale_api_client - INFO - 正在处理supplier 17/215: 100016
2025-09-19 16:43:35,759 - finale_api_client - INFO - 正在处理supplier 18/215: 100017
2025-09-19 16:43:36,147 - finale_api_client - INFO - 正在处理supplier 19/215: 100018
2025-09-19 16:43:36,497 - finale_api_client - INFO - 正在处理supplier 20/215: 100019
2025-09-19 16:43:36,905 - finale_api_client - INFO - 正在处理supplier 21/215: 100020
2025-09-19 16:43:37,300 - finale_api_client - INFO - 正在处理supplier 22/215: 100021
2025-09-19 16:43:37,732 - finale_api_client - INFO - 正在处理supplier 23/215: 100022
2025-09-19 16:43:38,115 - finale_api_client - INFO - 正在处理supplier 24/215: 100023
2025-09-19 16:43:38,506 - finale_api_client - INFO - 正在处理supplier 25/215: 100024
2025-09-19 16:43:38,864 - finale_api_client - INFO - 正在处理supplier 26/215: 100025
2025-09-19 16:43:39,207 - finale_api_client - INFO - 正在处理supplier 27/215: 100026
2025-09-19 16:43:39,554 - finale_api_client - INFO - 正在处理supplier 28/215: 100027
2025-09-19 16:43:39,879 - finale_api_client - INFO - 正在处理supplier 29/215: 100028
2025-09-19 16:43:40,207 - finale_api_client - INFO - 正在处理supplier 30/215: 100029
2025-09-19 16:43:40,570 - finale_api_client - INFO - 正在处理supplier 31/215: 100030
2025-09-19 16:43:40,936 - finale_api_client - INFO - 正在处理supplier 32/215: 100031
2025-09-19 16:43:41,268 - finale_api_client - INFO - 正在处理supplier 33/215: 100032
2025-09-19 16:43:41,715 - finale_api_client - INFO - 正在处理supplier 34/215: 100033
2025-09-19 16:43:42,062 - finale_api_client - INFO - 正在处理supplier 35/215: 100034
2025-09-19 16:43:42,510 - finale_api_client - INFO - 正在处理supplier 36/215: 100035
2025-09-19 16:43:42,899 - finale_api_client - INFO - 正在处理supplier 37/215: 100036
2025-09-19 16:43:43,253 - finale_api_client - INFO - 正在处理supplier 38/215: 100037
2025-09-19 16:43:43,611 - finale_api_client - INFO - 正在处理supplier 39/215: 100038
2025-09-19 16:43:43,940 - finale_api_client - INFO - 正在处理supplier 40/215: 100039
2025-09-19 16:43:44,338 - finale_api_client - INFO - 正在处理supplier 41/215: 100040
2025-09-19 16:43:44,725 - finale_api_client - INFO - 正在处理supplier 42/215: 100041
2025-09-19 16:43:45,385 - finale_api_client - INFO - 正在处理supplier 43/215: 100042
2025-09-19 16:43:45,792 - finale_api_client - INFO - 正在处理supplier 44/215: 100043
2025-09-19 16:43:46,108 - finale_api_client - INFO - 正在处理supplier 45/215: 100044
2025-09-19 16:43:46,442 - finale_api_client - INFO - 正在处理supplier 46/215: 100045
2025-09-19 16:43:47,036 - finale_api_client - INFO - 正在处理supplier 47/215: 100046
2025-09-19 16:43:47,352 - finale_api_client - INFO - 正在处理supplier 48/215: 100047
2025-09-19 16:43:47,711 - finale_api_client - INFO - 正在处理supplier 49/215: 100048
2025-09-19 16:43:48,079 - finale_api_client - INFO - 正在处理supplier 50/215: 100049
2025-09-19 16:43:48,464 - finale_api_client - INFO - 正在处理supplier 51/215: 100050
2025-09-19 16:43:48,807 - finale_api_client - INFO - 正在处理supplier 52/215: 100051
2025-09-19 16:43:49,157 - finale_api_client - INFO - 正在处理supplier 53/215: 100052
2025-09-19 16:43:49,506 - finale_api_client - INFO - 正在处理supplier 54/215: 100053
2025-09-19 16:43:49,832 - finale_api_client - INFO - 正在处理supplier 55/215: 100054
2025-09-19 16:43:50,165 - finale_api_client - INFO - 正在处理supplier 56/215: 100055
2025-09-19 16:43:50,522 - finale_api_client - INFO - 正在处理supplier 57/215: 100056
2025-09-19 16:43:51,172 - finale_api_client - INFO - 正在处理supplier 58/215: 100057
2025-09-19 16:43:51,485 - finale_api_client - INFO - 正在处理supplier 59/215: 100058
2025-09-19 16:43:51,812 - finale_api_client - INFO - 正在处理supplier 60/215: 100059
2025-09-19 16:43:52,183 - finale_api_client - INFO - 正在处理supplier 61/215: 100060
2025-09-19 16:43:52,545 - finale_api_client - INFO - 正在处理supplier 62/215: 100061
2025-09-19 16:43:52,867 - finale_api_client - INFO - 正在处理supplier 63/215: 100062
2025-09-19 16:43:53,230 - finale_api_client - INFO - 正在处理supplier 64/215: 100063
